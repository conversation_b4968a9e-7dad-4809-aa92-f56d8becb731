<template>
    <div
        class="c-description text-sm"
        :class="{ 'min-h-7 flex justify-between gap-2': layout !== 'vertical' }"
    >
        <slot name="label">
            <Tip
                data-aos="fade-left"
                data-aos-delay="50"
                data-aos-anchor="#app"
                class="c-description__label leading-7 text-title truncate flex items-center"
                :class="labelClass"
                :tip
            >
                <span class="mr-0.5">{{ label }}</span>
            </Tip>
        </slot>

        <div
            data-aos="fade-left"
            data-aos-delay="100"
            data-aos-anchor="#app"
            class="c-description__value leading-7 font-digit truncate font-semibold normal-case whitespace-pre-line"
            :class="[ ..._valueClass, layout !== 'vertical' ? 'flex-1 w-full text-right' : 'w-full' ]"
        >
            <slot>
                <template v-if="_.isFunction(value)">
                    <component :is="value()"/>
                </template>
                <template v-else-if="_.isObject(value)">
                    <component :is="value"/>
                </template>
                <template v-else>
                    {{ _.isNil(value) ? '-' : value }}
                </template>
            </slot>
        </div>
    </div>
</template>

<script setup>
import _ from 'lodash'

import Tip from '@/components/Tip/index.vue'

const { valueClass, color } = defineProps({
    label: [ String, Number, Object, Function ],
    value: [ String, Number, Object, Function ],
    layout: String,
    labelClass: [ String, Array, Object ],
    valueClass: [ String, Array, Object ],
    color: String,
    tip: String,
})

const _valueClass = computed(() => _.concat(valueClass, color, { 'text-primary': !color }))

defineOptions({ name: 'C-Description' })
</script>

<style scoped>
</style>
