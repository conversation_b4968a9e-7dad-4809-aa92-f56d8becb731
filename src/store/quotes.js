import _ from 'lodash'

// 默认股票表格排序
export const DEFAULT_STOCK_SORT = {
    field: 'volume',
    order: 'DESC',
}

// 行情路由状态
export const $quotes_route = useSessionStorage('quotes_route', 'quotes-stock')

// 行情状态管理
export const useQuotesStore = defineStore('quotes', () => {
    const { push } = useRouter()

    /**
     * @const marketType 市场 A股 | 港股 |美股
     * */
    const $marketType = useSessionStorage('marketType', STOCK_CONFIG.CN.symbol),
        $markets = computed(() => STOCK_CONFIG[$marketType.value].markets)

    /**
     * @const plateType 市场大盘类型
     * */
    const $plateOptions = computed(() => STOCK_CONFIG[$marketType.value].plate),
        $plateType = useSessionStorage('plateType', $plateOptions.value[0])

    /**
     * @const securityType 证券类型
     * 1	股票	港股、美股、沪深
     * 2	指数	港股、美股、沪深
     * 3	其他	港股、美股、沪深
     * 11	轮证	港股
     * 12	牛熊证	港股
     * 13	界内证	港股
     * 14	ETF	港股
     * 15	债券	港股
     * 16	权证	港股
     * 41	ETF	美股
     * */
    const $securityType = useSessionStorage('securityType', 1)

    // 1:行业板块 2:概念板块
    // const plateType = useSessionStorage('plateType', 1)

    // 板块参数
    const $plateParams = useSessionStorage('plateParams', {
        markets: $plateOptions.value[0],
        plateId: '',
        securityType: 1,
        title: '',
    })

    const dispatch_checkIndustryType = ({ name, key, market }) => {
        $plateParams.value = {
            markets: market ?? STOCK_CONFIG[$marketType.value].markets,
            plateId: key,
            securityType: 1,
            title: name,
        }
        push('/industry/type')
    }

    return {
        $marketType,
        $markets,
        $securityType,

        $plateOptions,
        $plateType,
        $plateParams,
        dispatch_checkIndustryType,
    }
})

// export const INDEX_SYMBOLS = [
//     // 上证指数
//     'SSE|2|000001',
//     // 深证成指
//     'SZSE|2|399001',
//     // 创业板指
//     'SZSE|2|399006',
//     // 恒生香港中资企业指数
//     'HKEX|2|HSCCI',
//     // 国企指数
//     'HKEX|2|HSCEI',
//     // 恒生指数
//     'HKEX|2|HSI',
//     // 道琼斯
//     'US|2|.DJI',
//     // 纳斯达克
//     'US|2|.IXIC',
//     // 标普500
//     'US|2|.INX',
// ]

let intervalInstance

// 股指状态管理
export const useIndexStore = defineStore('index', () => {
    const { name } = useRoute()

    const stockStore = useStockStore(),
        { dispatch_checkStock } = stockStore,
        { $currentStock } = storeToRefs(stockStore)

    const { res: $indexData, originalRes, initial: $indexInitial, onRefresh: dispatch_refreshIndex } = useRequest({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%82%A1%E6%8C%87%E9%85%8D%E7%BD%AE/getConfigUsingGET_1
        url: '/stock/index/getConfig',
        initialValues: {},
        formatResult: (res) => {
            const obj = {}

            res.sort((a, b) => a?.sortOrder - b?.sortOrder).forEach((e, i) => {
                const { market, securityType, symbol } = e

                // 根据配置可用的股指设置默认数据类型
                const instrument = [ market, securityType, symbol ].join('|')
                obj[instrument] = {
                    config: e,
                    timeline: [],
                    details: {
                        ...stockInfoInitial,
                    },
                    isShow: i <= 2,
                }

                Promise.all([
                    // 获取股指基础数据
                    (async () => {
                        const stockInfo = await onGetStockInfo({
                            instrument,
                        })
                        $indexData.value[instrument].details = stockInfo
                    })(),
                    // 获取股指k线图
                    (async () => {
                        const { list } = await api_get({
                            url: API_PATH.STOCK_TIME_LINE,
                            params: { instrument, period: 'day' },
                        })

                        if (list && list.length) $indexData.value[instrument].timeline = list
                    })(),
                ])
            })

            return obj
        },
        onSuccess: () => {
            if (name === 'quotes-index') {

                if (+$currentStock.value.securityType !== 2) {
                    const indexData = _.keys($indexData.value)
                    if (indexData.length) {
                        const [ market, securityType, symbol ] = indexData[0].split('|')
                        dispatch_checkStock({ market, securityType, symbol }, { redirect: false })
                    }
                }
            }
        },
    })

    document.addEventListener('visibilitychange', async () => {
        if (document.visibilityState === 'visible') {
            await dispatch_refreshIndex()
        }
    })

    return {
        $indexInitial,
        $indexData,
        originalRes,
        dispatch_refreshIndex,
    }
})
