import { STOCK_ROUTE } from '@/config/index.js'

// 股票市场默认的股票配置
export const MARKET_DEFAULT_SYMBOL = {
    CN: {
        market: 'SZSE',
        symbol: '000001',
        securityType: 1,
    },
    HK: {
        market: 'HKEX',
        symbol: '00001',
        securityType: 1,
    },
    US: {
        market: 'US',
        symbol: 'AAPL',
        securityType: 1,
    },
}

const volumeInitial = {
    ask: [],
    bid: [],
    symbol: '',
    market: '',
    securityType: '',
    close: 0,
    latestPrice: 0,
}

// 股票状态管理
export const useStockStore = defineStore('stock', () => {
    const router1 = useRouter()
    const { push, replace } = router1

    const $stockActiveTab = ref(STOCK_ROUTE.DETAILS_NAME)
    const $minuteType = ref('realtime_day')

    // 当前股票的数据
    const $currentStock = useSessionStorage('currentStock', {
            ...MARKET_DEFAULT_SYMBOL.CN,
        }),
        // 是否股指
        $isIndex = computed(() => +$currentStock.value.securityType === 2),
        // 股票接口查询所需的字符串参数
        $instrument = computed(() => {
            const { market, securityType, symbol } = $currentStock.value
            return [ market, securityType, symbol ].join('|')
        })

    /**
     * @function dispatch_checkStock
     * @description 根据传递的股票信息进行操作
     * @param stockInfo {object} 需要查看的股票信息
     * @param stockInfo.symbol {string} 股票代码
     * @param stockInfo.market {string} 股票市场
     * @param stockInfo.securityType {number} 股票类型 1：股票，2：股指
     * @param [option] {object} 配置项
     * @param [option.routeName] {string} 需要跳转的路由
     * @param [option.isReplace] {boolean} 是否采用替换跳转路由的方式
     * @param [option.params] {object} 路由参数
     * @param [option.redirect] {boolean} 是否跳转需要路由
     * */
    const dispatch_checkStock = async (stockInfo, option) => {
        const { routeName = STOCK_ROUTE.DETAILS, isReplace, params, redirect = true, isBack = false } = option ?? {}

        // 赋值当前股票信息
        $currentStock.value = {
            ...stockInfo,
        }

        // 加载对应股票数据
        const fetches = [
            dispatch_getStock({
                instrument: $instrument.value,
            }),
        ]

        // 个股查询交易盘
        if (!$isIndex.value) {
            fetches.push(
                volumeRun({
                    instrument: $instrument.value,
                    depth: 10,
                }),
            )
        } else {
            $volume.value = { ...volumeInitial }
        }

        if (redirect) {
            // 路由跳转方式
            const router = isReplace ? replace : push

            let path, name
            if ($isIndex.value && routeName === STOCK_ROUTE.TRANSACTION) {
                name = STOCK_ROUTE.TRANSACTION_INDEX
            } else if (isBack) {
                router1.back()
            } else {
                path = `/stock/${stockInfo.securityType}/${routeName}`

                if (routeName === STOCK_ROUTE.DETAILS && sessionStorage.getItem('stockDetailsTab') !== 'quotes') {
                    sessionStorage.setItem('stockDetailsTab', 'quotes')
                }
            }

            fetches.push(
                router({
                    path,
                    name,
                    query: params,
                }),
            )
        }

        await Promise.all(fetches)
    }

    // 股票基础数据
    const $stock = useSessionStorage('stock', {
        ...stockInfoInitial,
    })
    const dispatch_getStock = async (params) => {
        $stock.value = await onGetStockInfo(params)
        // if ($isIndex.value) $stock.value.lotSize = .1
    }

    // 涨跌幅状态
    const $raise_fall = computed(() => {
        const gain = $stock.value.gain

        return {
            raise: gain > 0,
            fall: gain < 0,
            flat: gain === 0,
            color: utils_amount_color(gain),
        }
    })

    // 交易体量
    const { res: $volume, run: volumeRun } = useRequest({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/getDepthQuoteL2UsingGET_1
        url: '/market/depth/l2',
        manual: true,
        initialValues: { ...volumeInitial },
        cancellable: false,
    })

    // 股票页刷新后自动加载
    const dispatch_refreshStock = () => dispatch_checkStock($currentStock.value, { redirect: false })
    dispatch_refreshStock()

    return {
        $instrument,
        $currentStock,
        $isIndex,
        dispatch_checkStock,
        dispatch_refreshStock,
        $stock,
        $raise_fall,
        $volume,
        $stockActiveTab,
        $minuteType,
    }
})

export const useMarketStatusStore = defineStore('marketStatus', () => {
    // 市场状态
    const { res: $marketStatus, onRefresh: dispatch_refreshMarketStatus } = useRequest({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/getCombinedMarketStatusUsingGET_1
        url: '/market/status/combine',
        params: {
            marketTypes: 'US|HK|CN',
        },
        initialValues: {
            list: [],
        },
    })

    return {
        $marketStatus,
        dispatch_refreshMarketStatus,
    }
})
