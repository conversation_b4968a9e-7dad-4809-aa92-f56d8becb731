<template>
    <div class="h-(--header-height) flex-middle gap-2.5 px-4 overflow-hidden" data-aos="fade-down">
        <!-- Logo -->
        <img
            class="w-[100px] h-8"
            alt="logo"
            :src="$logo"
        />
        <!-- Logo -->

        <!-- 搜索栏 -->

        <Search
            class="flex-1"
            readonly
            @click="$router.push({
                name: 'stock-search',
                params: {
                    globalSearch: true
                }
            })"
        />
        <!-- 搜索栏 -->

        <!-- 一级菜单 -->
        <van-popover
            placement="bottom-end"
            :close-on-click-action="false"
            :show-arrow="false"
            v-model:show="menuPopover"
        >
            <template #reference>
                <van-badge :dot="Boolean(+$unreadCount)">
                    <c-icon name="menu"/>
                </van-badge>
            </template>

            <div class="menu-item" @click="$router.push('/message')">
                <c-icon name="notice"/>

                <van-badge :content="$unreadCount" max="99">
                    {{ t('message') }}
                </van-badge>
            </div>

            <div
                class="menu-item"
                v-for="({ title, key }, i) in config"
                @click="actionType = key; secondPopover = true; index = i;"
            >
                <c-icon :name="key"/>

                <span>
                    {{ title }}
                </span>
            </div>
        </van-popover>
        <!-- 一级菜单 -->

        <!-- 二级菜单 -->
        <van-popover
            placement="left-start"
            trigger="manual"
            :offset="[ 18 + (index + 1) * 44, locale === 'en-US' ? 160 : 130 ]"
            :show-arrow="false"
            v-model:show="secondPopover"
        >
            <div
                class="menu-item"
                v-for="({ title, key, active }) in optionConfig.actions"
                @click="onSelect(key, active)"
            >
                <c-icon :name="key"/>

                <span :class="{ 'text-active': active }">
                    {{ title }}
                </span>
            </div>
        </van-popover>
        <!-- 二级菜单 -->
    </div>
</template>

<script setup>
import Search from '@/components/Search/index.vue'

const { $logo } = storeToRefs(useGlobalStore())

const { $unreadCount } = storeToRefs(useMessageStore())

const { config, actionType, optionConfig } = useSystemSetting()

const { t, locale } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            message: '我的消息',
        },
        [LANGUAGE.zhHK]: {
            message: '我的訊息',
        },
        [LANGUAGE.enUS]: {
            message: 'message',
        },
    },
})

// 菜单显隐开关
const menuPopover = ref(false),
    secondPopover = ref(false),
    index = ref(0)

const onSelect = (key, active) => {
    if (!active) unref(optionConfig).onSelect(key)

    secondPopover.value = false
}

defineOptions({ name: 'ActionsBar' })
</script>

<style scoped>
.menu-item {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 44px;
    padding: 0 12px;
    cursor: var(--text);
}
</style>
