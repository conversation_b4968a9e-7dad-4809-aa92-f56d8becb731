<template>
    <van-pull-refresh
        class="profile h-full overflow-auto p-4"
        :disabled="!$isLogin"
        :model-value="loading"
        @refresh="_onRefresh"
    >
        <!-- 基础信息 -->
        <div data-aos="fade-left">
            <div v-if="!$isLogin" class="flex">
                <div class="w-1/5 ml-auto">
                    <van-button
                        round
                        block
                        size="small"
                        type="primary"
                        to="/auth"
                    >
                        {{ t('auth.login') }}
                    </van-button>
                </div>
            </div>

            <div v-else class="flex-middle gap-2.5 text-sm">
                <c-avatar
                    :avatar="$profile.avatar"
                    @click="$router.push('/avatar')"
                />

                <div class="baseInfo" @click="$router.push('/profile')">
                    <div class="normal-case flex-middle gap-2.5">
                        <span>{{ $profile.nickname }}</span>
                        <img
                            v-if="$profile.auth"
                            :src="verifyIcon[locale]"
                            alt="verify"
                            class="h-4"
                        >
                    </div>
                    <div>{{ $profile.mobile }}</div>
                </div>

                <c-icon
                    class="ml-auto"
                    prefix="mine"
                    size="40"
                    :name="`vip_${$profile.level}`"
                    @click="$router.push('/vip')"
                />
            </div>
        </div>
        <!-- 基础信息 -->

        <!-- 资产 -->
        <c-card
            class="my-4"
            data-aos-delay="50"
        >
            <div class="text-title text-sm mb-2.5 grid grid-cols-[1fr_1fr_auto] gap-2.5 text-center">
                <div>
                    <div>{{ t('account.balance') }}</div>
                    <c-rate-currency
                        :amount="$spot.usableCash"
                        :disabled="!$isLogin"
                        v-model:currency="currency"
                        v-model:rate="rate"
                    />
                </div>
                <div>
                    <div>{{ t('account.interest') }}</div>
                    <c-amount :amount="$spot.interestCash * rate"/>
                </div>
                <div>
                    <van-button
                        type="primary"
                        size="mini"
                        @click="$router.push('/spot/financial/true')"
                    >
                        {{ t('_fund') }}
                    </van-button>
                </div>
            </div>

            <div class="bg-controller_bg rounded-lg h-10 flex-between px-4">
                <span class="text-sm">{{ t('account.freeze') }}</span>
                <c-amount :amount="$spot.freezeCash * rate"/>
            </div>
        </c-card>
        <!-- 资产 -->

        <c-card
            data-aos-delay="150"
            class="flex-middle mb-4"
            v-if="$globalConfig.service"
        >
            <img
                src="./assets/service.png"
                alt="service"
                class="w-16"
            >

            <div class="flex-1 ml-5">
                <div class="mr-auto text-title">
                    {{ t('_exclusive') }}
                </div>

                <van-button
                    type="primary"
                    size="mini"
                    @click="handleToService"
                >
                    {{ t('_service') }}
                </van-button>
            </div>
        </c-card>

        <!-- 快捷导航 -->
        <c-card data-aos-delay="200">
            <van-grid
                :border="false"
                clickable
            >
                <van-grid-item
                    data-aos="zoom-in"
                    v-for="({ title, icon, to, handler }, i) in nav"
                    :data-aos-delay="i * 50 + 50"
                    :key="icon"
                    :text="title"
                    :to
                    @click="handler"
                >
                    <template #icon>
                        <c-icon
                            prefix="mine"
                            size="30"
                            theme
                            :name="icon"
                        />
                    </template>
                </van-grid-item>
            </van-grid>
        </c-card>
        <!-- 快捷导航 -->
    </van-pull-refresh>
</template>

<script setup>
import verification_zhCN from './assets/<EMAIL>'
import verification_zhHK from './assets/<EMAIL>'
import verification_enUS from './assets/<EMAIL>'
import _ from 'lodash'
import i18n from '@/i18n/index.js'

const router = useRouter()
const verifyIcon = {
    [LANGUAGE.zhCN]: verification_zhCN,
    [LANGUAGE.zhHK]: verification_zhHK,
    [LANGUAGE.enUS]: verification_enUS,
}

const profileStore = useProfileStore(),
    { dispatch_refreshProfile } = profileStore,
    { $isLogin, $profile, $profileLoading } = storeToRefs(profileStore),
    accountStore = useAccountStore(),
    { dispatch_refreshSpot, dispatch_resetAccount } = accountStore,
    { $spot, $spotLoading } = storeToRefs(accountStore),
    { $globalConfig } = storeToRefs(useGlobalStore())

const onDownload = useDownload()

const [ currency, rate ] = useRate()

const loading = computed(() => $spotLoading.value || $profileLoading.value)

const _onRefresh = async () => {
    await Promise.all([
        dispatch_refreshSpot(),
        dispatch_refreshProfile(),
    ])
}

/*专属客服修改：
1如果有上级，进入聊天默认和这个代理线的头部代理对话聊天，
2如果没有上级，就无法进入专属客服，提示，您无专属客服，请联系在线客服
3无论这个会员是否是开启聊天功能还是是否能登录聊天后台，都按1，2执行*/

const handleToService = _.debounce(async () => {
    const { pImAccount, pNickname } = await api_get({
        url: '/tc/getUserKefu',
    })

    if (pImAccount && pNickname) {
        router.push(`/service/${pImAccount}`)
    } else {
        showDialog({
            title: i18n.global.t('server.tip'),
        })
    }
}, 500, { leading: true })

const { onLogoutConfirm } = useLogout()

const nav = computed(() => [
    {
        title: 'VIP',
        icon: 'vip',
        to: '/vip',
    },
    {
        title: t('header.mission'),
        icon: 'mission',
        to: '/mission',
    },
    {
        title: t('_financial'),
        icon: 'financial',
        handler: () => {
            accountActiveTab.value = ACCOUNT_TYPE.SPOT
        },
        to: '/account',
    },
    {
        title: t('header.third'),
        icon: 'third',
        to: '/deposit/third',
    },
    {
        title: t('_invite'),
        icon: 'invite',
        to: '/invite',
    },
    {
        title: t('header.q&a'),
        icon: 'question',
        to: '/q&a',
    },
    {
        title: t('header.authentication'),
        icon: 'authentication',
        to: '/authentication',
    },
    {
        title: t('header.setting'),
        icon: 'setting',
        to: '/setting',
    },
    {
        title: t('header.about'),
        icon: 'information',
        to: '/about',
    },
    {
        title: t('_download'),
        icon: 'download',
        handler: onDownload,
    },
    {
        title: t('_exchange'),
        icon: 'exchange',
        to: '/exchange',
    },
    {
        title: t('auth.logout'),
        icon: 'logout',
        handler: onLogoutConfirm,
    },
])

const { t, locale } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _exclusive: '专属客服通道',
            _entry: '立即进入',
            _financial: '入金/出金',
            _invite: '邀请返佣',
            _newbie: '新手专区',
            _authentication: '实名认证',
            _download: 'App 下载',
            _exchange: '汇率查询',
            _service: '立即咨询',
            _fund: '资金记录',
        },
        [LANGUAGE.zhHK]: {
            _exclusive: '专属客服通道',
            _entry: '立即进入',
            _financial: '入金/出金',
            _invite: '邀请返佣',
            _newbie: '新手专区',
            _authentication: '实名认证',
            _download: 'App 下载',
            _exchange: '汇率查询',
            _service: '立即咨询',
            _fund: '資金記錄',
        },
        [LANGUAGE.enUS]: {
            _exclusive: 'Exclusive Service',
            _entry: 'Entry',
            _financial: 'Financial',
            _invite: 'Invite',
            _newbie: 'Newbie',
            _authentication: 'AuthN',
            _download: 'Download',
            _exchange: 'Exchange',
            _service: 'Consult now',
            _fund: 'Fund Record',
        },
    },
})

watch(() => $isLogin, (bool) => {
    if (!bool.value) {
        dispatch_resetAccount()
    }
}, { immediate: true })


defineOptions({ name: 'profile' })
</script>

<style scoped>
.van-theme-dark .baseInfo {
    color: var(--title);
}

.profile {
    background-image: url('assets/watermark.png'), url('assets/bg.png');
    background-repeat: no-repeat;
    background-position: right 20px, top;
    background-size: 150px, contain;
    overflow: hidden;
}

.van-grid {
    --van-grid-item-text-color: var(--title);
    --van-grid-item-content-padding: 8px;
}
</style>
