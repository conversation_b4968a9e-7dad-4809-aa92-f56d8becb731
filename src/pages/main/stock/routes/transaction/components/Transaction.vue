<template>
    <div v-bind="$attrs">
        <c-card>
            <slot/>

            <div class="h-8 flex-between mb-2.5 text-title text-sm">
                <!-- 股指交易类型选择 -->
                <template v-if="isIndex">
                    <!--class="flex-middle gap-2 text-xs"-->
                    <van-radio-group
                        class="gap-2 text-xs"
                        :icon-size="12"
                        v-model="tradeMode"
                    >
                        <!-- 持仓交易 -->
                        <van-radio :name="1">
                            {{ t('transaction.type_position') }}
                        </van-radio>
                        <!-- 持仓交易 -->

                        <!-- 定时平仓 -->
                        <van-radio :name="2" :disabled="timingDisabled">
                            <Tip :tip="t('_index_tip', [ currentIndexConfig.tradeUnit ])">
                                {{ t('transaction.type_timing') }}
                            </Tip>
                        </van-radio>
                        <!-- 定时平仓 -->
                    </van-radio-group>
                </template>
                <!-- 股指交易类型选择 -->

                <!-- 个股交易方向 -->
                <template v-else>
                    <span>{{ t('stock.transaction.direction') }}</span>
                </template>
                <!-- 个股交易方向 -->

                <!-- 交易方向选择：买入、卖出 -->
                <div v-if="!timingMode" class="flex gap-2">
                    <div
                        class="h-8 flex-center rounded-md px-4"
                        v-for="({ label, value, active }) in directionOptions"
                        :key="value"
                        :class="[ currentDirection === value ? active : 'bg-controller_bg', { 'opacity-60': tradeDisabled } ]"
                        @click="onChangeDirection(value)"
                    >
                        {{ label }}
                    </div>
                </div>
                <!-- 交易方向选择：买入、卖出 -->
            </div>

            <!-- 股指定时平仓内容 -->
            <template v-if="timingMode">
                <slot name="indexTiming" :position/>
            </template>
            <!-- 持仓交易内容 -->
            <template v-else>
                <!-- 交易配置 -->
                <div class="grid grid-cols-2 gap-2.5">
                    <!-- 下单账户 -->
                    <c-select
                        popover
                        :disabled="tradeDisabled"
                        :columns="accountOptions"
                        :placeholder="t('transaction.ways')"
                        v-model="accountSelected"
                        @select="onSelectAccount"
                    />
                    <!-- 下单账户 -->

                    <!-- 金额 -->
                    <c-controller>
                        <!-- 市价 -->
                        <template v-if="currentPrice === 1">
                            <div class="w-full text-center">
                                {{ t('transaction.price_realtime') }}
                            </div>
                        </template>
                        <!-- 市价 -->

                        <!-- 限价 -->
                        <template v-else>
                            <van-stepper
                                :min="0"
                                :decimal-length="$stock.precision"
                                :step="Math.pow(10, -$stock.precision)"
                                :disabled="tradeDisabled"
                                v-model="customPrice"
                            />
                        </template>
                        <!-- 限价 -->
                    </c-controller>
                    <!-- 金额 -->

                    <!-- 订单类型 -->
                    <c-select
                        popover
                        :columns="priceOptions"
                        :disabled="tradeDisabled"
                        v-model="currentPrice"
                    />
                    <!-- 订单类型 -->

                    <!-- 交易数量 -->
                    <c-controller>
                        <van-stepper
                            :min="0"
                            :max="quantityMax"
                            :step="$stock.lotSize"
                            :decimal-length="quantityStepperProps.decimalLength"
                            :integer="quantityStepperProps.integer"
                            :disabled="tradeDisabled"
                            v-model="quantity"
                            @change="onChangeQuantity"
                        />
                    </c-controller>
                </div>
                <!-- 交易配置 -->

                <!-- 可用余额 -->
                <div ref="domRef" class="flex-between my-2.5 text-sm text-primary">
                    <span class="font-semibold">{{ t('account.balance') }}</span>

                    <c-amount
                        :amount="currentAccount.balance"
                        :currency="currentAccount.currency"
                    />
                </div>
                <!-- 可用余额 -->

                <!-- 预设仓位 -->
                <c-picker
                    clickable
                    :disabled="tradeDisabled"
                    :options="presets"
                    @change="onPreset"
                />
                <!-- 预设仓位 -->

                <!-- 买入、做多 -->
                <template v-if="currentDirection === 1">
                    <StockTransactionPriceDetails
                        :direction="currentDirection"
                        :label="directionLabel.buyAvailable"
                        :available="availableBuy"
                        :data-source="priceDetails"
                        :currency="marketCurrency"
                        :totalRate
                        :isIndex
                    />

                    <van-button
                        :color="directionLabel.buyBtnColor"
                        block
                        :disabled="disabled || !availableBuy"
                        :loading
                        @click="onConfirm(0)"
                    >
                        <!--买入开多-->
                        {{ directionLabel.buyBtn }}
                    </van-button>

                    <!--<template v-if="!isCNMarket || (isIndex && currentIndexConfig.isToShort)">-->
                    <template v-if="showOpenShow">
                        <StockTransactionPriceDetails
                            :direction="currentDirection"
                            :label="directionLabel.buyShortAvailable"
                            :available="availableBuy"
                            :data-source="priceDetails"
                            :currency="marketCurrency"
                            :totalRate
                            :isIndex
                        />

                        <van-button
                            :color="directionLabel.buyShortBtnColor"
                            block
                            :disabled="disabled || !availableBuy"
                            :loading
                            @click="onConfirm(1)"
                        >
                            <!--卖出开空-->
                            {{ directionLabel.buyShortBtn }}
                        </van-button>
                    </template>
                </template>
                <!-- 买入、做多 -->

                <!-- 卖出、做空 -->
                <template v-if="currentDirection === 2">
                    <!--<template v-if="(!isCNMarket || isIndex) && currentIndexConfig.isToShort">-->
                    <template v-if="showOpenShow">
                        <!-- 做空持仓选项 -->
                        <IndexPositionSelector
                            v-if="isIndex"
                            :disabled="tradeDisabled"
                            :columns="shortPosition"
                            v-model="shortPositionId"
                            @select="onSelectPosition"
                        />
                        <!-- 做空持仓选项 -->

                        <StockTransactionPriceDetails
                            :direction="currentDirection"
                            :label="directionLabel.sellShortAvailable"
                            :available="closeAvailableBuy"
                            :data-source="priceDetails"
                            :currency="marketCurrency"
                            :totalRate
                            :isIndex
                        />

                        <van-button
                            :color="directionLabel.sellShortBtnColor"
                            block
                            :disabled="disabled || !closeAvailableBuy"
                            :loading
                            @click="onConfirm(2)"
                        >
                            <!--买入平空-->
                            {{ directionLabel.sellShortBtn }}
                        </van-button>
                    </template>

                    <!-- 做多持仓选项 -->
                    <IndexPositionSelector
                        v-if="isIndex"
                        :disabled="tradeDisabled"
                        :columns="longPosition"
                        v-model="longPositionId"
                        @select="onSelectPosition"
                    />
                    <!-- 做多持仓选项 -->

                    <StockTransactionPriceDetails
                        :direction="currentDirection"
                        :label="directionLabel.sellAvailable"
                        :available="closeAvailableSell"
                        :data-source="priceDetails"
                        :currency="marketCurrency"
                        :totalRate
                        :isIndex
                    />

                    <van-button
                        :color="directionLabel.sellBtnColor"
                        block
                        :disabled="disabled || !closeAvailableSell"
                        :loading
                        @click="onConfirm(3)"
                    >
                        <!--卖出开多-->
                        {{ directionLabel.sellBtn }}
                    </van-button>
                </template>
                <!-- 卖出、做空 -->
            </template>
        </c-card>

        <div class="mt-2.5 px-1 h-full">
            <van-tabs
                shrink
                class="h-full"
                v-model:active="activeTab"
            >
                <!-- 当前委托 -->
                <van-tab
                    v-if="!isIndex"
                    class="pt-2.5"
                    name="entrust"
                    :title="`${t('stock.entrust.processing')}(${entrustPagination.total})`"
                >
                    <EntrustTable
                        class="h-full"
                        @row-click="onRowClick($event, 'entrust')"
                    />
                </van-tab>
                <!-- 当前委托 -->

                <!-- 当前持仓 -->
                <van-tab
                    class="pt-2.5"
                    name="position"
                    :title="`${t('stock.position.position')}(${position.length})`"
                >
                    <div class="h-full overflow-auto">
                        <c-record
                            :finished="positionFinished"
                            :onLoadMore="onLoadMorePosition"
                            v-model:refresh-loading="positionRefreshLoading"
                            v-model:load-loading="positionLoadLoading"
                        >
                            <Position
                                v-for="item in position"
                                :key="item.id"
                                :data="item"
                                :onClick="$event => { onRowClick($event, 'position') }"
                            />
                        </c-record>
                    </div>
                </van-tab>
                <!-- 当前持仓 -->
            </van-tabs>
        </div>
    </div>

    <!-- 交易信息确认弹窗 -->
    <van-popup
        class="p-4 overflow-hidden"
        style="width: min(90%, 640px * .9)"
        round
        v-model:show="confirmPopup"
    >
        <div class="text-title text-center">
            {{ t('transaction.confirm') }}
        </div>

        <StockTransactionPriceDetails
            :label="t('stock.transaction.quantity')"
            :direction="currentDirection"
            :available="+quantity"
            :data-source="priceDetails"
            :currency="marketCurrency"
            :prefix="baseDetails"
            :totalRate
            :isIndex
        />

        <div class="mt-4 flex-middle gap-4">
            <div
                class="w-1/2"
                data-aos="fade-left"
                data-aos-anchor="#app"
            >
                <van-button
                    block
                    @click="confirmPopup = false"
                >
                    {{ t('operation.cancel') }}
                </van-button>
            </div>

            <c-submit
                class="w-1/2"
                :loading
                @click="onValid"
            />
        </div>
    </van-popup>
    <!-- 交易信息确认弹窗 -->

    <!-- 股票弹窗信息 -->
    <StockPopup
        :type="stockPopupType"
        :dataSource="stockPopupDetails"
        @refresh="onSelectAccount"
        @operation="onOperation"
        v-model="stockPopup"
    />
    <!-- 股票弹窗信息 -->
</template>

<script setup>
import currencyJs from 'currency.js'
import _ from 'lodash'

import { CURRENCY } from '@/config'
import Position from '@/pages/main/account/components/Position.vue'
import StockPopup from './StockPopup.vue'
import StockTransactionPriceDetails from './StockTransactionPriceDetails.vue'
import IndexPositionSelector from '@/pages/main/quotes/routes/index/components/IndexPositionSelector.vue'
import Tip from '@/components/Tip/index.vue'
import { API_PATH } from '@/apis/index.js'
import router from '@/router.js'

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            transaction: {
                ways: '选择交易方式',
                spot: '现货交易',
                available_buy: '可买',
                open: '开仓',
                open_long: '买入开多',
                open_short: '买入平空',
                available_open: '可开',
                sell_long: '卖出开多',
                sell_short: '卖出开空',
                available_sell: '卖出',
                close: '卖出',
                available_close: '卖出',
                type_position: '持仓交易',
                type_timing: '定时平仓',
                price_realtime: '以实时价格成交',
                price_market: '市价单',
                price_custom: '限价单',
                position_empty: '暂无可用持仓',
                position_all: '全仓',
                confirm: '订单确认',
                successfully: '订单交易成功',
                lot_error: '不符合购买股数',
            },
            _index_tip: '股指交易提示：当前1个股指点={0}倍',
        },
        [LANGUAGE.zhHK]: {
            transaction: {
                ways: '选择交易方式',
                spot: '现货交易',
                available_buy: '可买',
                open: '开仓',
                open_long: '买入开多',
                open_short: '买入平空',
                available_open: '可开',
                sell_long: '卖出开多',
                sell_short: '卖出开空',
                available_sell: '卖出',
                close: '卖出',
                available_close: '卖出',
                type_position: '持仓交易',
                type_timing: '定时平仓',
                price_realtime: '以实时价格成交',
                price_market: '市价单',
                price_custom: '限价单',
                position_empty: '暂无可用持仓',
                position_all: '全仓',
                confirm: '订单确认',
                successfully: '订单交易成功',
                lot_error: '不符合购买股数',
            },
            _index_tip: '股指交易提示：当前1个股指点={0}倍',
        },
        [LANGUAGE.enUS]: {
            transaction: {
                ways: 'Select Methods',
                spot: 'Spot Trading',
                available_buy: 'Available to Buy',
                open: 'Open',
                open_long: 'Open Long',
                open_short: 'Open Short',
                available_open: 'Available to Open',
                sell_long: 'Sell Long',
                sell_short: 'Sell Short',
                available_sell: 'Available to Sell',
                close: 'Close',
                available_close: 'Available to Close',
                type_position: 'Position',
                type_timing: 'Timed',
                price_realtime: 'Real-Time Price',
                price_market: 'Market Order',
                price_custom: 'Limit Order',
                position_empty: 'No Available',
                position_all: 'Full',
                confirm: 'Order Confirmation',
                successfully: 'Order Executed Successfully',
                lot_error: 'Does not meet the number of shares purchased',
            },
            _index_tip: 'Stock index trading tips: Current 1 stock index point = {0} times',
        },
    },
})

const { type, tradeDisabled } = defineProps({
    type: String,
    tradeDisabled: Boolean,
    timingDisabled: Boolean,
})

// 是否股指交易
const isIndex = type === 'index'

const { query } = useRoute(),
    contractId = query?.contractId ? +query.contractId : (isIndex ? '' : null)

// 股指/股票是否可以做空
const showOpenShow = computed(() => {
    if (isIndex) {
        return currentIndexConfig.value.isToShort
    } else {
        return !isCNMarket.value
    }
})

const accountStore = useAccountStore(),
    { dispatch_refreshAccount } = accountStore,
    { $spot, $contract } = storeToRefs(accountStore),
    { $indexData } = storeToRefs(useIndexStore()),
    stockStore = useStockStore(),
    { dispatch_refreshStock } = stockStore,
    { $currentStock, $stock, $instrument } = storeToRefs(stockStore),
    { dispatch_getCurrencyRateConfig } = useRateStore()

const activeTab = ref(isIndex ? 'position' : 'entrust')

const currentIndexConfig = computed(() => $indexData.value[$instrument.value]?.config ?? { tradeUnit: 0, isToShort: false })

// 当前股票市场
const stockMarket = computed(() => stockCountryDict.get($currentStock.value.market)),
    // 判断是否A股
    isCNMarket = computed(() => stockMarket.value === STOCK_CONFIG.CN.symbol),
    // 市场币种
    marketCurrency = computed(() => STOCK_CONFIG[stockMarket.value]?.currency ?? CURRENCY.CNY)

// 股指交易模式
const tradeMode = defineModel('mode', Number),
    // 定时平仓
    timingMode = computed(() => tradeMode.value === 2)

// 交易方向配置
const directionOptions = computed(() => {
        const options = [
            {
                label: isCNMarket.value && !isIndex
                    ? t('dict.买入')
                    : t('transaction.open'),
                value: 1,
                active: 'bg-raise text-white',
            },
            {
                label: isCNMarket.value && !isIndex
                    ? t('dict.卖出')
                    : t('transaction.close'),
                value: 2,
                active: 'bg-fall text-white',
            },
            {
                label: t('dict.txn_info'),
                value: 3,
            },
        ]

        // 如果不是指数，去掉 value = 3 的项（信息）
        if (!isIndex) {
            return options.slice(0, 2)
        }

        return options
    }),
    // 当前交易方向：默认买入、做多
    currentDirection = ref(1)

// 切换交易方向
const onChangeDirection = (direction) => {
    if (tradeDisabled) return

    if (+direction === 3) {
        // 跳转到交易详情
        router.push('/index/txnInfo')
    }

    // 卖出判断是否有持仓
    if (direction === 2 && !position.value.length) return showFailToast(t('transaction.position_empty'))

    if (currentDirection.value !== direction) {
        currentDirection.value = direction
        onRefreshFee()
    }
}

// 购买账户配置
const accountOptions = computed(() => {
        const spotOption = { text: t('transaction.spot'), value: '' }

        // 股指只允许用现货账户交易
        if (isIndex) return [ spotOption ]

        // 非股指交易可以选择合约
        return [
            spotOption,
            // 合约账户
            ..._.filter($contract.value, { marketType: stockCountryDict.get($currentStock.value.market) }).map(e => {
                return {
                    text: utils_contract_name(e),
                    value: e.id,
                }
            }),
        ]
    }),
    // 选中账户
    accountSelected = ref(contractId),
    // 当前账户
    currentAccount = computed(() => {
        let balance = 0,
            currency = $spot.value.currency

        if (accountSelected.value) {
            // 取合约可用余额
            const currentContract = _.find($contract.value, { id: accountSelected.value }) ?? {
                useAmount: 0,
                currency: CURRENCY.CNY,
            }
            balance = currentContract.useAmount
            currency = currentContract.currency
        } else if (accountSelected.value === '') {
            // 现货
            balance = $spot.value.usableCash
        }

        const { rate } = dispatch_getCurrencyRateConfig($stock.value.currency),
            // 对应市场按照汇率换算后的余额
            marketBalance = currency === $stock.value.currency ? balance : currencyJs(balance).multiply(rate).value

        return {
            balance,
            marketBalance,
            currency,
        }
    }),
    // 非A股现货，总价换算人民币汇率
    totalRate = computed(() => !accountSelected.value && !isCNMarket.value)

// 选择不同的账户后刷新持仓和委托数据
const onSelectAccount = async () => {
    await Promise.all([
        dispatch_refreshStock(),
        onRefreshEntrust(),
        onRefreshPosition(),
    ])
    quantity.value = quantityMax.value
}

// 成交价选项配置
const priceOptions = computed(() => {
        const base = [
            { text: t('transaction.price_market'), value: 1 },
            { text: t('transaction.price_custom'), value: 2 },
        ]
        if (isIndex) return base.slice(0, -1)
        return base
    }),
    // 当前成交价类型
    currentPrice = ref(1),
    // 限价：用户输入金额、默认股票最新价
    customPrice = ref($stock.value.latestPrice)

// 仓位预设配置
const presets = computed(() => [
    { label: '1/4', value: 0 },
    { label: '1/3', value: 1 },
    { label: '1/2', value: 2 },
    { label: t('transaction.position_all'), value: 3 },
])
// 设置预设仓位
const onPreset = type => {
    const available = quantityMax.value,
        lotSize = $stock.value.lotSize

    switch (type) {
        case 0:
            quantity.value = Math.floor(available / 4 / lotSize) * lotSize
            break
        case 1:
            quantity.value = Math.floor(available / 3 / lotSize) * lotSize
            break
        case 2:
            quantity.value = Math.floor(available / 2 / lotSize) * lotSize
            break
        case 3:
            quantity.value = available
            break
    }
}

// 下单数量步进器参数
const quantityStepperProps = computed(() => {
    const { lotSize } = $stock.value
    // 是否整数
    const integer = _.isInteger(lotSize)

    // 小数位数
    let decimalLength = 0
    if (!integer && !_.isNaN(+lotSize)) decimalLength = `${lotSize}`.split('.')[1].length

    return {
        decimalLength,
        integer,
    }
})

// 下单数量
const quantity = ref(0),
    // 下单上限
    quantityMax = computed(() => {
        let max
        // 买入取买入上限
        if (currentDirection.value === 1) {
            max = availableBuy.value
        } else if (isIndex) {
            // 股指平仓 取选择仓位的可用数量
            const selectPositionId = longPositionId.value || shortPositionId.value
            if (selectPositionId) {
                max = _.find(position.value, { id: selectPositionId })?.restNum ?? 0
            } else {
                max = Math.max(closeAvailableBuy.value, 0)
            }
        } else {
            max = Math.max(availableSell.value.long, availableSell.value.short)
        }

        return _.floor(max, quantityStepperProps.value.decimalLength)
    })

// 修改交易数量
const onChangeQuantity = val => {
    const lotSize = $stock.value.lotSize

    const remainder = currencyJs(+val).divide(lotSize)

    if (!_.isInteger(remainder)) quantity.value = _.floor(remainder) * lotSize
}

// 计算可买数量
const _utils_calcAvailable = ({ amount, price, decimal, lotSize }) => {
    const total = currencyJs(_.floor(amount / price, decimal)),
        available = currencyJs(_.floor(total.divide(lotSize))).multiply(lotSize).value

    return available < lotSize ? 0 : available
}

// 成交价：市价取股票最新价、限价取用户输入价格
const dealPrice = computed(() => {
        return currentPrice.value === 1 ? $stock.value.latestPrice : customPrice.value
    }),
    // 可买入数量
    availableBuy = computed(() => {
        const { lotSize } = $stock.value,
            _dealPrice = dealPrice.value

        // 交易价格
        if (!+_dealPrice || isNaN(+_dealPrice)) return 0

        const { marketBalance } = currentAccount.value,
            decimal = quantityStepperProps.value.decimalLength

        // 计算余额理论可买上限
        const principleAvailable = _utils_calcAvailable({
            amount: marketBalance,
            price: _dealPrice,
            decimal,
            lotSize,
        })

        // 理论可买上限手续费计算
        const fee = _utils_calcFee(currencyJs(principleAvailable, { precision: 6 }).multiply(_dealPrice)).buyFee

        // 减去手续费后的实际可买上限
        return _utils_calcAvailable({
            amount: marketBalance - fee,
            price: _dealPrice,
            decimal,
            lotSize,
        })
    }),
    // 可卖出数量
    availableSell = computed(() => {
        // 多单
        const long = _.filter(position.value, { tradeType: 1 }),
            // 空单
            short = _.filter(position.value, { tradeType: 2 })

        return {
            long: _.sumBy(long, 'restNum'),
            short: _.sumBy(short, 'restNum'),
        }
    }),
    // 平仓可买数量
    closeAvailableBuy = computed(() => {
        if (isIndex) {
            const selectPosition = _.find(shortPosition.value, { id: shortPositionId.value })
            return selectPosition ? selectPosition.restNum : 0
        }
        return availableSell.value.short
    }),
    // 平仓可买数量
    closeAvailableSell = computed(() => {
        if (isIndex) {
            const selectPosition = _.find(longPosition.value, { id: longPositionId.value })
            return selectPosition ? selectPosition.restNum : 0
        }
        return availableSell.value.long
    }),
    // 买卖开平对应文本
    directionLabel = computed(() => {
        return isCNMarket.value && !isIndex
            ? {
                buyAvailable: t('transaction.available_buy'),
                buyBtn: t('dict.买入'),
                buyBtnColor: 'var(--raise)',

                sellAvailable: t('transaction.available_sell'),
                sellBtn: t('dict.卖出'),
                sellBtnColor: 'var(--fall)',
            }
            : {
                buyAvailable: t('transaction.available_open'),
                buyBtn: t('transaction.open_long'), // 买入开多
                buyBtnColor: 'var(--raise)',

                buyShortAvailable: t('transaction.available_open'),
                buyShortBtn: t('transaction.sell_short'), // 卖出开空
                buyShortBtnColor: 'var(--fall)',

                sellAvailable: t('transaction.available_close'),
                sellBtn: t('transaction.sell_long'), // 卖出开多
                sellBtnColor: 'var(--fall)',

                sellShortAvailable: t('transaction.available_close'),
                sellShortBtn: t('transaction.open_short'), // 买入平空
                sellShortBtnColor: 'var(--raise)',
            }
    })

const disabled = computed(() => _.isNil(accountSelected.value) || tradeDisabled || !quantity.value)

onMounted(async () => {
    await nextTick()
    if (availableBuy.value) quantity.value = availableBuy.value
})

const _utils_calcFee = (total) => {
    // 手续费
    let buyFee = 0,
        sellFee = 0

    feeConfig.value.forEach(({ direction, calculateType, calculateValue, min, max, roundType, roundPrecision }) => {
        let _fee = 0
        switch (calculateType) {
            // % × 交易金额
            case 1:
                _fee = total.multiply(calculateValue).divide(100)
                break
            // 数量
            case 2:
                _fee = calculateValue * quantity.value
                break
            // 每笔
            case 3:
                _fee = calculateValue
                break
        }

        if (isIndex) _fee *= indexTradeUnit.value
        if (calculateType < 3) _fee = _.clamp(_fee, min, max)

        let roundFn
        switch (roundType) {
            // 进一
            case 1:
                roundFn = _.ceil
                break
            // 四舍五入
            case 2:
                roundFn = _.round
                break
            // 去尾
            case 3:
                roundFn = _.floor
                break
        }

        const fee = roundFn ? roundFn(_fee, roundPrecision) : _fee

        switch (direction) {
            // 买
            case 1:
                buyFee += fee
                break
            // 卖
            case 2:
                sellFee += fee
                break
            // 买卖
            case 3:
                buyFee += fee
                sellFee += fee
                break
        }
    })

    return {
        buyFee: _.round(buyFee, 2),
        sellFee: _.round(sellFee, 2),
    }
}

// 股指交易杠杆
const indexTradeUnit = computed(() => {
    if (isIndex) {
        return $indexData.value[$instrument.value]?.config?.tradeUnit ?? 1
    } else {
        return 1
    }
})

// 费用详情
const priceDetails = computed(() => {
    const order = currencyJs(quantity.value, { precision: 6 }).multiply(dealPrice.value)

    const { buyFee, sellFee } = _utils_calcFee(order)

    return {
        buyFee: buyFee,
        sellFee: sellFee,
        order,
        buyTotal: Math.max(order.add(buyFee), 0),
        sellTotal: Math.max(order.subtract(sellFee), 0),
    }
})

// 下单确认弹窗开关
const confirmPopup = ref(false),
    // 交易类型
    orderType = ref(0),
    // 交易类型参数
    orderConfig = computed(() => {
        return {
            0: {
                direction: 1,
                directionLabel: directionLabel.value.buyBtn,
                directionColor: 'text-raise',
                tradeType: 1,
            },
            1: {
                direction: 1,
                directionLabel: directionLabel.value.buyShortBtn,
                directionColor: 'text-fall',
                tradeType: 2,
            },
            2: {
                direction: 2,
                directionLabel: directionLabel.value.sellShortBtn,
                directionColor: 'text-raise',
                tradeType: 2,
            },
            3: {
                direction: 2,
                directionLabel: directionLabel.value.sellBtn,
                directionColor: 'text-fall',
                tradeType: 1,
            },
        }[orderType.value]
    })

// 基础确认详情
const baseDetails = computed(() => {
    const { directionLabel, directionColor } = orderConfig.value

    return [
        // 交易方向
        {
            label: t('stock.transaction.direction'),
            value: () => h(
                'span',
                {
                    class: directionColor,
                },
                directionLabel,
            ),
        },
        // 账户类型
        { label: t('account.type'), value: accountOptions.value.find(e => e.value === accountSelected.value).text },
        // 订单类型
        {
            label: t('stock.entrust.type'),
            value: priceOptions.value.find(e => e.value === currentPrice.value).text,
        },
        // 交易价格
        {
            label: t('stock.transaction.price'),
            value: dealPrice.value,
        },
    ]
})

// 下单确认
const onConfirm = (type) => {
    orderType.value = type
    confirmPopup.value = true
}

const onValid = () => {
    if (tradeDisabled) return

    const integer = currencyJs(quantity.value, { decimal: quantityStepperProps.value.decimalLength }).divide($stock.value.lotSize).intValue
    if (!_.isInteger(integer)) {
        return showFailToast(t('transaction.lot_error'))
    }

    onSubmit()
}

// 下单提交
const [ onSubmit, loading ] = useFetchLoading(async () => {
    const { direction, tradeType } = orderConfig.value

    // 股指平仓需要的仓位ID参数
    let positionId
    if (isIndex) positionId = tradeType === 2 ? shortPositionId.value : longPositionId.value

    try {
        await api_post({
            // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E8%AE%A2%E5%8D%95%E7%9B%B8%E5%85%B3/createOrderUsingPOST_1
            url: '/order/createOrder',
            params: {
                contractAccountId: accountSelected.value,
                priceType: currentPrice.value,
                market: $stock.value.market,
                securityType: $stock.value.securityType,
                symbol: $stock.value.symbol,
                symbolName: $stock.value.name,
                // toFixed 有小数的情况无法卖出
                tradeNum: quantity.value.toFixed(2),
                tradePrice: dealPrice.value,
                direction,
                tradeType,
                positionId,
                expireType: isIndex ? 1 : 2,
                tradeUnit: indexTradeUnit.value,
            },
        })

        if (isIndex) {
            longPositionId.value = undefined
            shortPositionId.value = undefined
        }

        confirmPopup.value = false

        showSuccessToast(t('transaction.successfully'))

        dispatch_refreshAccount()
        onSelectAccount()
    } finally {
        if (+orderType.value === 0 || +orderType.value === 2) {
            onClearIntervalInstance()
            handleRefresh()
        }
        dispatch_refreshStock()
    }
})

const queryParams = computed(() => {
    const { symbol, market, securityType } = $currentStock.value

    const params = {
        symbol,
        market,
        securityType,
        direction: currentDirection.value,
        status: 0,
    }

    if (accountSelected.value) {
        params['contractId'] = accountSelected.value
    } else {
        params['commentAssetId'] = $spot.value.assetId
    }
    return params
})

// 股票弹窗开关
const stockPopup = ref(false),
    // 弹窗类型
    stockPopupType = ref('entrust'),
    // 弹窗数据详情
    stockPopupDetails = ref({})

// 委托、持仓点击查看详情
const onRowClick = (details, type) => {
    stockPopupDetails.value = details
    stockPopupType.value = type
    stockPopup.value = true
}

const domRef = useTemplateRef('domRef')

// 当前持仓操作
const onOperation = (type, { id, tradeType }) => {
    switch (type) {
        case 'buy':
            quantity.value = availableBuy.value
            onChangeDirection(1)
            break
        case 'sell':
        case 'close':
            onChangeDirection(2)
            quantity.value = tradeType === 1 ? availableSell.value.long : availableSell.value.short
            if (isIndex) {
                if (tradeType === 1) {
                    longPositionId.value = id
                } else {
                    shortPositionId.value = id
                }
            }
            break
    }

    if (domRef.value) {
        domRef.value.scrollIntoView({ smooth: true, block: 'start' })
    }
    stockPopup.value = false
}

// 当前委托数据
const {
    Table: EntrustTable,
    list: entrustList,
    initial: entrustInitial,
    pagination: entrustPagination,
    onRefresh: onRefreshEntrust,
} = useEntrustTable({
    params: queryParams,
    manual: isIndex,
})

// 持仓数据
const {
    list: position,
    initial: positionInitial,
    refreshLoading: positionRefreshLoading,
    loadLoading: positionLoadLoading,
    pagination: positionPagination,
    finished: positionFinished,
    onRefresh: onRefreshPosition,
    onLoadMore: onLoadMorePosition,
} = usePositionFetch({
    params: queryParams,
    needLogin: true,
    onSuccess: (_res, _params) => {
        if (!_res.length && currentDirection.value === 2) {
            currentDirection.value = 1
        }

        if (isIndex) {
            longPositionId.value = undefined
            shortPositionId.value = undefined
        }
    },
})

// 做多持仓
const longPosition = computed(() => _.filter(position.value, { tradeType: 1 })),
    // 做空持仓
    shortPosition = computed(() => _.filter(position.value, { tradeType: 2 }))

const longPositionId = ref(),
    shortPositionId = ref()

// 股指平仓选择
const onSelectPosition = ({ id }) => {
    const selectedPosition = _.find(position.value, { id })
    if (selectedPosition) quantity.value = selectedPosition.restNum
}

// 手续费
const { res: feeConfig, onRefresh: onRefreshFee } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E6%94%B6%E8%B4%B9%E9%A1%B9/getChargeByMarketUsingGET_1
    url: '/charge/getByMarket',
    params: queryParams,
    needLogin: true,
    initialValues: [],
})

// 切换股指
const onChangIndex = async () => {
    await Promise.all([
        onSelectAccount(),
        onRefreshFee(),
    ])

    quantity.value = availableBuy.value
    customPrice.value = $stock.value.latestPrice
}

let intervalInstance

const onClearIntervalInstance = () => {
    clearInterval(intervalInstance)
    intervalInstance = null
}

onBeforeUnmount(onClearIntervalInstance)

// const marketStatusRun = (data) => {
//     const marketTypes = _.map(data, e => stockCountryDict.get(e.market))
//     return useMarketStatus(marketTypes)
// }


const handleRefresh = async () => {
    await nextTick()

    intervalInstance = setInterval(async () => {
        switch (activeTab.value) {
            case 'entrust':
                if (entrustInitial.value && entrustList.value.length) {
                    const res = await api_get({
                        url: API_PATH.ENTRUST,
                        params: {
                            ...queryParams.value,
                            status: 0,
                            pageNumber: 1,
                            pageSize: entrustPagination.value.current * entrustPagination.value.pageSize,
                        },
                    })
                    entrustList.value = res.records
                } else {
                    onClearIntervalInstance()
                }
                break
            case 'position':
                if (positionInitial.value && position.value.length) {
                    const res = await api_get({
                        url: API_PATH.POSITION,
                        params: {
                            ...queryParams.value,
                            status: 0,
                            pageNumber: 1,
                            pageSize: positionPagination.value.current * positionPagination.value.pageSize,
                        },
                    })
                    position.value = res?.records
                } else {
                    onClearIntervalInstance()
                }
                break
        }
    }, 5000)
}

onMounted(handleRefresh)


defineExpose({
    onChangIndex,
})

defineOptions({ name: 'Transaction', inheritAttrs: false })
</script>

<style scoped>
.van-stepper {
    width: 100%;
    display: flex;
    --van-stepper-background: var(--controller_bg);
}

:deep(.van-stepper__input) {
    flex: 1;
}
</style>
