<template>
    <c-header>
        <van-tabs
            class="w-max mx-auto"
            type="card"
            v-model:active="$stockActiveTab"
            @click-tab="onClickTab"
        >
            <van-tab
                :title="$t('stock.transition')"
                :name="STOCK_ROUTE.TRANSACTION"
            />
            <van-tab
                :title="$t('stock.quotes')"
                :name="STOCK_ROUTE.DETAILS_NAME"
            />
        </van-tabs>

        <template #right>
            <van-icon
                name="search"
                class="mr-2.5"
                @click="push('/stock/search')"
            />
        </template>
    </c-header>

    <div class="with-header-container__noPadding">
        <router-view/>
    </div>
</template>

<script setup>
import { STOCK_ROUTE } from '@/config'

const route = useRoute()
const { onRedirect } = useStockRedirect()

const onClickTab = ({ name }) => {
    onRedirect(name)
    $stockActiveTab.value = name
}

const { push } = useRouter()

const { $stockActiveTab } = storeToRefs(useStockStore())

$stockActiveTab.value = route.path.includes('transaction')
    ? 'transaction'
    : 'stock-details'

// const onRedirectQuotes = () => {
//     const marketType = stockCountryDict.get($currentStock.value.market)
//     $marketType.value = marketType
//     $plateType.value = STOCK_CONFIG[marketType].plate[0]
//
//     const redirect = +$currentStock.value.securityType === 1 ? '/quotes/stock' : '/quotes/index'
//     push(redirect)
// }

onBeforeUnmount(() => {
    $stockActiveTab.value = STOCK_ROUTE.DETAILS_NAME
})

// 个股详情
defineOptions({ name: 'stock' })
</script>

<style scoped>
:deep(.c-header__left) {
    width: 42px;
}
</style>
