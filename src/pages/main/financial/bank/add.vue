<template>
    <c-header :title="t('financial.add_bank')"/>

    <form class="with-header-container" @submit="onValid">
        <c-card :title="t('financial.bank_number')">
            <c-input
                data-aos="fade-left"
                data-aos-delay="100"
                :placeholder="t('form.input_placeholder', [ t('financial.bank_number') ])"
                minlength="10"
                maxlength="19"
                inputmode="numeric"
                v-model="formState.bankCardNo"
            />
        </c-card>

        <c-card
            class="mt-4 mb-10"
            no-padding
            data-aos-delay="100"
        >
            <van-form
                label-width="120px"
            >
                <van-field
                    data-aos="fade-left"
                    data-aos-delay="50"
                    :label="t('financial.bank_owner')"
                    :model-value="$profile.realName"
                    autocomplete="off"
                    clearable
                    @clear="$profile.realName = ''"
                />
                <van-field
                    data-aos="fade-left"
                    data-aos-delay="150"
                    :label="t('_bank')"
                    is-link
                    readonly
                    required
                    @click="popup = true"
                    :model-value="res[selectedIndex]?.bankName ?? ''"
                />
                <van-field
                    data-aos="fade-left"
                    data-aos-delay="200"
                    :label="t('profile.mobile')"
                    v-model="$profile.mobile"
                    autocomplete="off"
                    clearable
                    @clear="$profile.mobile = ''"
                />
                <van-field
                    data-aos="fade-left"
                    data-aos-delay="250"
                    :label="t('auth.otp')"
                    :placeholder="t('form.input_placeholder', [ t('auth.otp') ])"
                    type="digit"
                    :minlength="6"
                    :maxlength="6"
                    required
                    clearable
                    v-model="formState.smsCode"
                >
                    <template #button>
                        <van-button
                            size="mini"
                            type="primary"
                            :loading="otpLoading"
                            :disabled="otpDisabled"
                            @click="onSendOtp($profile.mobile)"
                        >
                            {{ otpText }}
                        </van-button>
                    </template>
                </van-field>
            </van-form>
        </c-card>

        <c-submit
            data-aos-delay="200"
            :loading
            :disabled="disabled || selectedIndex === -1"
        />
    </form>

    <van-popup
        position="bottom"
        v-model:show="popup"
    >
        <van-picker
            :loading="bankLoading"
            :columns="res"
            :columns-field-names="{
                text: 'bankName',
                value: 'id'
            }"
            @cancel="popup = false"
            @confirm="onConfirm"
        >
            <template #empty>
                <van-empty :description="t('common.empty')"/>
            </template>
        </van-picker>
    </van-popup>
</template>

<script setup>
const { back } = useRouter()

const { $profile } = storeToRefs(useProfileStore()),
    { dispatch_refreshBank } = useBankStore()

const formState = ref({
    bankCardNo: '',
    smsCode: '',
})

const disabled = useFormDisabled(formState)

const { otpText, otpLoading, otpDisabled, onSendOtp } = useOtp({ type: OTP_TYPES.BIND })

const popup = ref(false),
    selectedIndex = ref(-1)

const onConfirm = ({ selectedIndexes }) => {
    selectedIndex.value = selectedIndexes[0]
    popup.value = false
}

const { res, loading: bankLoading } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E9%93%B6%E8%A1%8C%E5%8D%A1/bankListUsingGET_1
    url: '/bank/list',
    initialValues: [],
})

const onValid = (e) => {
    e.preventDefault()

    const { bankCardNo, smsCode } = formState.value,
        systemBankId = res.value[selectedIndex.value]?.id ?? 0

    if (!REGULAR.BANK.test(bankCardNo)) return showFailToast(t('financial.bank_error'))
    if (!systemBankId) return showFailToast(t('form.select_placeholder', [ t('_bank') ]))
    if (!REGULAR.OTP.test(smsCode)) return showFailToast(t('auth.otp_error'))

    onSubmit()
}

const [ onSubmit, loading ] = useFetchLoading(async () => {
    const { bankCardNo, smsCode } = formState.value,
        systemBankId = res.value[selectedIndex.value]?.id ?? 0

    try {
        await api_post({
            // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E9%93%B6%E8%A1%8C%E5%8D%A1/addUserBankUsingGET_1
            url: '/bank/addUserBank',
            params: {
                bankCardNo,
                systemBankId,
                mobile: $profile.value.mobile,
                smsCode,
            },
        })

        showSuccessToast(t('operation.successfully'))

        back()
    } finally {
        dispatch_refreshBank()
    }
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _bank: '开户行',
        },
        [LANGUAGE.zhHK]: {
            _bank: '开户行',
        },
        [LANGUAGE.enUS]: {
            _bank: 'Opening bank',
        },
    },
})

defineOptions({ name: 'bank-add' })
</script>

<style scoped>

</style>
