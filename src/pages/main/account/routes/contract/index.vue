<template>
    <AccountDetailsLayout
        :title
        :type="ACCOUNT_TYPE.CONTRACT"
        :currency="contractDetails.currency"
        :total="contractDetails.allAsset"
        :earnings="contractDetails.todayWinAmount"
        :balance="contractDetails.useAmount"
        :interest="contractDetails.interestAmount"
        :freeze="contractDetails.freezePower"
        :grid
        disabled-rate
    >
        <AccountTabs
            class="account-tabs-container"
            :type="ACCOUNT_TYPE.CONTRACT"
        />
    </AccountDetailsLayout>
</template>

<script setup>
import { ACCOUNT_TYPE } from '@/config'
import AccountTabs from '@/pages/main/account/components/AccountTabs.vue'
import AccountDetailsLayout from '@/pages/main/account/components/AccountDetailsLayout.vue'

const { dispatch_checkStock } = useStockStore()

const { id: contractId } = useAccountId(),
    contractDetails = useCurrentContract()

const title = computed(() => utils_contract_name(contractDetails.value))

const grid = computed(() => {
    const base = [
        // 扩大保证金
        {
            icon: 'grid_expand',
            title: t('contract.expand'),
            to: '/contract/expand/' + contractId,
            showType: [ 1, 3 ],
        },
        // 追加合约
        {
            icon: 'grid_replenish',
            title: t('contract.replenish'),
            to: '/contract/replenish/' + contractId,
            showType: [ 1, 3 ],
        },
        // 合约提盈
        {
            icon: 'grid_withdrawal',
            title: t('contract.withdrawal'),
            to: '/contract/withdrawal/' + contractId,
            showType: [ 1, 3 ],
        },
        // 交易中心
        {
            icon: 'grid_transaction',
            title: t('account.transaction'),
            click: () => {
                dispatch_checkStock(MARKET_DEFAULT_SYMBOL[contractDetails.value.marketType], {
                    routeName: STOCK_ROUTE.TRANSACTION,
                    params: { contractId },
                })
            },
        },
        // 资金记录
        {
            icon: 'grid_finance',
            title: t('account.finance_record'),
            to: '/contract/financial/' + contractId,
        },
        // 合约续费
        // {
        //     icon: 'grid_renewal',
        //     title: t('contract.renewal'),
        //     to: '/contract/renewal/' + contractId,
        //     showType: [ 1 ],
        // },
        // 终止合约
        {
            icon: 'grid_finish',
            title: t('contract.finish'),
            to: '/contract/finish/' + contractId,
        },
        // 历史订单
        {
            icon: 'grid_history',
            title: t('account.history'),
            to: '/contract/order/' + contractId,
        },
        // 合约详情
        {
            icon: 'contract-details',
            title: t('contract.details'),
            to: '/contract/details/' + contractId,
        },
    ]

    return base.filter(e => !('showType' in e) || e?.showType.includes(contractDetails.value.type))
})

const { t } = useI18n()

// 合约账户
defineOptions({ name: 'contract' })
</script>

<style scoped>
.account-tabs-container {
    /*
        MarginTop 16
        总资产 100
        资产统计 64
        快捷导航 176
    */
    height: calc(100% - 16px - 100px - 64px - 176px);
}
</style>
