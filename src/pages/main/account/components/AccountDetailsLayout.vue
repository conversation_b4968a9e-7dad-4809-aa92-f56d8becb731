<template>
    <c-header v-if="showTitle" :title/>

    <div class="with-header-container__noPadding">
        <main
            class="custom-shadow rounded-bl-lg rounded-br-lg"
            data-aos="fade-left"
            data-aos-delay="50ms"
        >
            <!-- 总资产 -->
            <div class="card text-white mx-4 rounded-lg overflow-hidden custom-shadow">
                <!-- 总资产、收益 -->
                <div class="p-5 flex-between text-xl">
                    <!--现货账户-->
                    <div v-if="type === 'spot'">
                        <!--type: spot contract-->
                        <div>{{ t('account.held_assets') }}</div>
                        <c-rate-currency
                            :amount="total"
                            :disabled="disabledRate"
                            v-model:currency="_currency"
                            v-model:rate="rate"
                        />
                    </div>
                    <!--合约账户-->
                    <div v-else>
                        <div>{{ t('account.total_assets') }}</div>
                        <c-rate-currency
                            :amount="total"
                            :disabled="disabledRate"
                            v-model:currency="_currency"
                            v-model:rate="rate"
                        />
                    </div>

                    <!--合约账户-->
                    <div v-if="type === 'spot'" class="text-right">
                        <div>{{ t('account.floatingProfitAndLoss') }}</div>
                        <c-amount :amount="earnings * rate"/>
                    </div>
                    <!--现货账户-->
                    <div v-else class="text-right">
                        <div>{{ t('account.today_earnings') }}</div>
                        <c-amount :amount="earnings * rate"/>
                    </div>

                </div>
                <!-- 总资产、收益 -->

                <!-- 资产统计 -->
                <Divider class="card-footer flex-middle text-center py-3" :theme="THEME_CONFIG.DARK">
                    <div
                        class="flex-1 w-1"
                        v-for="({ title, value }, i) in assets"
                        :key="i"
                    >
                        <div class="text-xs">{{ title }}</div>
                        <c-amount :amount="value"/>
                    </div>
                </Divider>
                <!-- 资产统计 -->
            </div>
            <!-- 总资产 -->

            <!-- 快捷导航 -->
            <van-grid v-bind="gridProps" :border="false">
                <van-grid-item
                    v-for="({ title, icon, to, click }, i) in grid"
                    :key="i"
                    :text="title"
                    :to
                    @click="click"
                >
                    <template #icon>
                        <c-icon
                            prefix="account"
                            size="32"
                            :name="icon"
                        />
                    </template>
                </van-grid-item>
            </van-grid>
            <!-- 快捷导航 -->
        </main>

        <slot/>
    </div>
</template>

<script setup>
import { THEME_CONFIG } from '@/config/index.js'
import Divider from '@/components/Divider/index.vue'

const { type, balance, interest, freeze, disabledRate, currency, earnings } = defineProps({
    showTitle: {
        type: Boolean,
        default: true,
    },
    // 页面标题
    title: {
        type: String,
    },
    // 账户类型：spot 现货、contract 合约
    type: {
        type: String,
        required: true,
    },
    // 总资产
    total: {
        type: Number,
        required: true,
    },
    // 今日收益
    earnings: {
        type: Number,
        required: true,
    },
    // 可用余额
    balance: {
        type: Number,
        required: true,
    },
    // 利息券
    interest: Number,
    // 冻结金额
    freeze: {
        type: Number,
        required: true,
    },
    // 快捷导航
    grid: {
        type: Array,
        required: true,
    },
    // Grid组件参数
    gridProps: Object,
    // 是否禁用汇率切换（主要用户合约账户）
    disabledRate: Boolean,
    // 当前币种
    currency: [ String, Number ],
})

// 资源数据渲染配置
const assets = computed(() => {
    const base = [
        {
            title: t('account.balance'),
            value: balance * rate.value,
        },
        {
            title: t('account.freeze'),
            value: freeze * rate.value,
        },
    ]

    if (type === ACCOUNT_TYPE.SPOT) {
        base.splice(1, 0, {
            title: t('account.interest'),
            value: interest,
        })
    }

    return base
})

const [ _currency, rate ] = useRate({ disabled: disabledRate })

watchEffect(() => {
    _currency.value = currency
})

const { t } = useI18n()

// 账户详情布局组件
defineOptions({ name: 'AccountDetailsLayout' })
</script>

<style scoped>
.card {
    background: linear-gradient(90deg, #4366DE 0%, #3150BD 100%);
}

.card-footer {
    background: rgba(255, 255, 255, .1);
}

.van-theme-dark main {
    background: rgba(255, 255, 255, .04);
}

.van-grid {
    --van-grid-item-content-padding: 16px 0;
    --van-grid-item-text-color: var(--title)
}

:deep(.van-grid-item__text) {
    margin-top: 4px;
}
</style>
