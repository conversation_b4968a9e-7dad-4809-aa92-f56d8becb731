<template>
    <div class="px-4 mb-1.25">
        <van-tabs
            class="main-tab"
            type="card"
            title-inactive-color="var(--paragraph)"
            shrink
            v-model:active="accountActiveTab"
        >
            <van-tab
                v-for="({title, key, name}) in accountTabs"
                :key="key"
                :title="title"
                :name="name"
            />
        </van-tabs>

        <van-tabs v-if="accountActiveTab === 'spot'" v-model:active="spotActiveTab">
            <van-tab v-for="({title, key, name}) in tabs" :key :title :name/>
        </van-tabs>
    </div>
</template>

<script setup>
const { t } = useI18n()
import { spotActiveTab } from '@/store/account.js'

import { accountActiveTab } from '@/store'

const accountTabs = computed(() => {
    return [
        {
            title: t('account.spot'),
            key: 'x_001',
            name: 'spot',
        },
        {
            title: t('account.contract'),
            key: 'x_002',
            name: 'contract',
        },
    ]
})

// 数据类型 1:A股 2：港股 3：美股 4：股指 5：国内期货
const tabs = computed(() => {
    return [
        {
            key: 'x_001',
            title: 'A股',
            name: 1,
        },
        {
            key: 'x_002',
            title: '港股',
            name: 2,
        },
        {
            key: 'x_003',
            title: '美股',
            name: 3,
        },
        {
            key: 'x_004',
            title: '股指',
            name: 4,
        },
        // TODO 01. 国内期货
        // {
        //     key: 'x_005',
        //     title: '国内期货',
        //     name: 5,
        // },
    ]
})

defineProps({
    title: 'Account-Top-Tabs',
})
</script>

<style scoped>
:deep(.main-tab > .van-tabs__wrap) {
    background: var(--card);
    border-radius: 32px;
}

:deep(.main-tab .van-tabs__nav--card) {
    display: flex;
    margin: 0;
    border-radius: 32px;
    border: none;
    justify-content: space-between;
}


:deep(.main-tab .van-tabs__nav--card > .van-tab) {
    height: 80%;
    flex: 1;
    border: none;
    border-radius: 32px;
}
</style>
