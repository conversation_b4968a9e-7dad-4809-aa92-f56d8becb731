<template>
    <div class="with-header-container__noPadding">
        <TopTabs/>

        <component
            :is="activeCom"
        />
    </div>
</template>

<script setup>
import { computed } from 'vue'
import TopTabs from '@/pages/main/account/components/TopTabs.vue'
import Contract from '@/pages/main/account/components/Contract.vue'
import Spot from '@/pages/main/account/components/Spot.vue'
import { accountActiveTab } from '@/store'

const activeCom = computed(() => {
    return accountActiveTab.value === 'spot' ? Spot : Contract
})
defineOptions({ name: 'account' })
</script>
