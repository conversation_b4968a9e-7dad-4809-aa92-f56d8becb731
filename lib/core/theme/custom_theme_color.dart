import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/theme/constant/template_a.dart';
import 'package:gp_stock_app/core/theme/constant/template_b.dart';
import 'package:gp_stock_app/core/theme/constant/template_c.dart';
import 'package:gp_stock_app/core/theme/constant/template_d.dart';

import 'constant/gp.dart';

class CustomColorThemeHelper {
  static CustomColorTheme getThemeLight() {
    switch (AppConfig.instance.skinStyle) {
      case AppSkinStyle.kGP:
        return CustomColorThemeConstantGP.lightDefault;
      case AppSkinStyle.kTemplateA:
        return CustomColorThemeConstantTemplateA.lightDefault;
      case AppSkinStyle.kTemplateB:
        return CustomColorThemeConstantTemplateB.lightDefault;
      case AppSkinStyle.kTemplateC:
        return CustomColorThemeConstantTemplateC.lightDefault;
      case AppSkinStyle.kTemplateD:
        return CustomColorThemeConstantTemplateD.lightDefault;
    }
  }

  static CustomColorTheme getThemeDark() {
    switch (AppConfig.instance.skinStyle) {
      case AppSkinStyle.kGP:
        return CustomColorThemeConstantGP.dartDefault;
      case AppSkinStyle.kTemplateA:
        return CustomColorThemeConstantTemplateA.darkDefault;
      case AppSkinStyle.kTemplateB:
        return CustomColorThemeConstantTemplateB.dartDefault;
      case AppSkinStyle.kTemplateC:
        return CustomColorThemeConstantTemplateC.dartDefault;
      case AppSkinStyle.kTemplateD:
        return CustomColorThemeConstantTemplateD.dartDefault;
    }
  }
}

/// 自定义颜色主题，用于配合 [CustomTextTheme] 构建统一配色方案
/// Custom color theme used to define unified colors across the app
///
/// 可通过 [context.colorTheme] 获取当前主题颜色集
/// Access via [context.colorTheme] in your widgets
class CustomColorTheme extends ThemeExtension<CustomColorTheme> {
  final Color textPrimary; // 主要文字 Main text
  final Color textTitle; // 标题文字 Title text
  final Color textRegular; // 常规文字 Regular text
  final Color textSecondary; // 次级文字 Secondary text
  final Color textHighlight; // 高亮文字 Highlight text
  final Color tabActive; // 激活标签页文字 Tab active color
  final Color tabInactive; // 非激活标签页文字 Tab inactive color
  final Color buttonPrimary; // 按钮主色 Button primary color
  final Color buttonSecondary; // 次按钮颜色 Secondary button color
  final Color stockRed; // 股票红色 Stock red
  final Color stockGreen; // 股票绿色 Stock green
  final Color border; // 边框颜色 Border color
  final Color pending; // 中性状态（待审核等） Neutral status (e.g. pending)
  final Color inviteBackgroundStart; // 邀请页面背景渐变起始色 Invite screen background gradient start
  final Color inviteBackgroundEnd; // 邀请页面背景渐变结束色 Invite screen background gradient end

  const CustomColorTheme({
    required this.textPrimary,
    required this.textTitle,
    required this.textRegular,
    required this.textSecondary,
    required this.textHighlight,
    required this.tabActive,
    required this.tabInactive,
    required this.buttonPrimary,
    required this.buttonSecondary,
    required this.stockRed,
    required this.stockGreen,
    required this.border,
    required this.pending,
    required this.inviteBackgroundStart,
    required this.inviteBackgroundEnd,
  });

  /// 拷贝当前配色，可用于动态生成主题
  @override
  CustomColorTheme copyWith({
    Color? textPrimary,
    Color? textTitle,
    Color? textRegular,
    Color? textSecondary,
    Color? textHighlight,
    Color? tabActive,
    Color? tabInactive,
    Color? buttonPrimary,
    Color? buttonSecondary,
    Color? stockRed,
    Color? stockGreen,
    Color? border,
    Color? pending,
    Color? inviteBackgroundStart,
    Color? inviteBackgroundEnd,
  }) {
    return CustomColorTheme(
      textPrimary: textPrimary ?? this.textPrimary,
      textTitle: textTitle ?? this.textTitle,
      textRegular: textRegular ?? this.textRegular,
      textSecondary: textSecondary ?? this.textSecondary,
      textHighlight: textHighlight ?? this.textHighlight,
      tabActive: tabActive ?? this.tabActive,
      tabInactive: tabInactive ?? this.tabInactive,
      buttonPrimary: buttonPrimary ?? this.buttonPrimary,
      buttonSecondary: buttonSecondary ?? this.buttonSecondary,
      stockRed: stockRed ?? this.stockRed,
      stockGreen: stockGreen ?? this.stockGreen,
      border: border ?? this.border,
      pending: pending ?? this.pending,
      inviteBackgroundStart: inviteBackgroundStart ?? this.inviteBackgroundStart,
      inviteBackgroundEnd: inviteBackgroundEnd ?? this.inviteBackgroundEnd,
    );
  }

  /// 用于主题切换动画渐变插值
  @override
  CustomColorTheme lerp(ThemeExtension<CustomColorTheme>? other, double t) {
    if (other is! CustomColorTheme) return this;
    return CustomColorTheme(
      textPrimary: Color.lerp(textPrimary, other.textPrimary, t)!,
      textTitle: Color.lerp(textTitle, other.textTitle, t)!,
      textRegular: Color.lerp(textRegular, other.textRegular, t)!,
      textSecondary: Color.lerp(textSecondary, other.textSecondary, t)!,
      textHighlight: Color.lerp(textHighlight, other.textHighlight, t)!,
      tabActive: Color.lerp(tabActive, other.tabActive, t)!,
      tabInactive: Color.lerp(tabInactive, other.tabInactive, t)!,
      buttonPrimary: Color.lerp(buttonPrimary, other.buttonPrimary, t)!,
      buttonSecondary: Color.lerp(buttonSecondary, other.buttonSecondary, t)!,
      stockRed: Color.lerp(stockRed, other.stockRed, t)!,
      stockGreen: Color.lerp(stockGreen, other.stockGreen, t)!,
      border: Color.lerp(border, other.border, t)!,
      pending: Color.lerp(pending, other.pending, t)!,
      inviteBackgroundStart: Color.lerp(inviteBackgroundStart, other.inviteBackgroundStart, t)!,
      inviteBackgroundEnd: Color.lerp(inviteBackgroundEnd, other.inviteBackgroundEnd, t)!,
    );
  }
}
