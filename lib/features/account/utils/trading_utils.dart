import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../../core/utils/utils.dart';

class TradingUtils {
  static String formatNumber(num? value, {int decimalPlaces = 3, bool isPercent = false}) {
    if (value == null) return "0.00 ${isPercent ? '%' : ''}";
    if (isPercent) return "${value.toStringAsFixed(decimalPlaces)}%";
    return value.toStringAsFixed(decimalPlaces);
  }

  static String formatPercentage(
    num? value, {
    bool isPercent = true,
    int decimalPlaces = 2,
  }) {
    if (value == null) return "--";
    return "${value.toStringAsFixed(decimalPlaces)}${isPercent ? '%' : ''}";
  }

  static String getSign(double? gain) {
    if (gain == null || gain == 0) return '';
    return gain > 0 ? '+' : '';
  }

  static String formatPrice(num? value, {int precision = 2}) {
    // Changed precision to 2
    if (value == null || value == 0) return "--";
    if (value >= 1000) {
      return "${(value / 1000).toStringAsFixed(precision)}K";
    }
    return value.toStringAsFixed(precision);
  }

  static String formatVolume(num? value, BuildContext context) {
    if (value == null || value == 0) return "--";
    return formatLargeNumber(value.toDouble(), context.locale.languageCode, precision: 2);
  }

  static String formatLotSize(num? value) {
    if (value == null || value == 0) return "--";
    return "$value ${'lotForSecurities'.tr()}";
  }
}
