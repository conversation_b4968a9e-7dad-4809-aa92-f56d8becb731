import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/constants/kline_constants.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_bottomsheet_future_sltp.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_bottomsheet_orders.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_dialog_add_margin.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/market_category_state.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_cubit_v2.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_all_info_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_scroll_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/features/market/widgets/sliver_app_bar_delegate.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/models/route_arguments/trading_arguments.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'list_view_cell/account_entrust_cell.dart';
import 'list_view_cell/account_position_cell.dart';
import 'list_view_cell/account_trade_cell.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class AccountOrderListView extends StatefulWidget {
  final MarketCategory marketCategory;
  final OrderType type;
  final OrderListState orderListState;
  final Function(bool isLoadMore) onFetch;

  const AccountOrderListView({
    super.key,
    required this.marketCategory,
    required this.type,
    required this.orderListState,
    required this.onFetch,
  });

  @override
  State<StatefulWidget> createState() => _AccountOrderListViewState();
}

class _AccountOrderListViewState extends State<AccountOrderListView> {
  // with AutomaticKeepAliveClientMixin {
  // @override
  // bool get wantKeepAlive => true;

  final RefreshController _refreshController = RefreshController();

  late OrderListState model = widget.orderListState;

  @override
  void initState() {
    super.initState();
    // 初始化时请求第一次数据
    widget.onFetch(false);
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant AccountOrderListView oldWidget) {
    if (widget.orderListState != oldWidget.orderListState) {
      setState(() {
        model = widget.orderListState;
      });
    }
    super.didUpdateWidget(oldWidget);
  }

  void _onRefresh() async {
    await widget.onFetch.call(false);
    _refreshController
      ..resetNoData()
      ..refreshCompleted();
  }

  void _onLoadMore() async {
    /// 过滤首页为空时加载更多
    if (model.records.isNotEmpty) {
      await widget.onFetch.call(true);
    }
    if (model.hasMoreData) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TradingCubit(),
      child: CommonRefresher(
        controller: _refreshController,
        enablePullDown: false,
        enablePullUp: true,
        onRefresh: () => _onRefresh(),
        onLoading: () => _onLoadMore(),
        child: CustomScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          slivers: [
            SliverOverlapInjector(
              // This is the flip side of the SliverOverlapAbsorber
              // above.
              handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
            ),


            if (model.records.isEmpty) ...[
              if (model.isInitialLoad && model.status == DataStatus.loading) ...[
                ///骨架图
                SliverToBoxAdapter(child: _buildShimmerWidget(widget.type)),
              ] else ...[
                /// 空视图
                SliverFillRemaining(
                    child: TableEmptyWidget(
                  height: 40,
                  width: 40,
                  title: switch (widget.type) {
                    OrderType.positions => "no_holdings".tr(),
                    OrderType.trades => "no_trades".tr(),
                    OrderType.order => "no_entrusts".tr(),
                  },
                  margin: EdgeInsets.symmetric(horizontal: 18.gw),
                  radius: 10.gw,
                )),
              ]
            ],

            if (model.records.isNotEmpty)
              SliverPadding(
                padding: EdgeInsets.symmetric(horizontal: 18.gw),
                sliver: SliverList.separated(
                  itemCount: model.records.length,
                  separatorBuilder: (_, __) =>
                      widget.type == OrderType.positions ? 10.verticalSpace : const SizedBox.shrink(),
                  itemBuilder: (context, index) {
                    final item = model.records[index];
                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 600),
                      child: SlideAnimation(
                        verticalOffset: 30.0,
                        child: FadeInAnimation(
                          child: _buildCell(context, widget.marketCategory, item, index),
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCell(BuildContext context, MarketCategory category, FTradeAcctOrderRecords item, int index) {
    final cubit = context.read<AccountScreenCubitV2>();
    return switch (widget.type) {
      OrderType.positions => AccountPositionCell(
          marketCategory: category,
          data: item,
          onTap: () async {
            if (category == MarketCategory.cnFutures) {
              await Navigator.pushNamed(
                context,
                routeFTradeAllInfo,
                arguments: (
                  FTradeMarketType.fromMarketCode(item.market).idx,
                  FTradeAllInfoTitlesType.quotation,
                  item.toFTradeListItemModel()
                ),
              ).then((value) {
                if (!context.mounted) return;
                cubit.fetchSpotScreenCurrentData();
              });
              return;
            }
            await Navigator.pushNamed(
              context,
              routeTradingCenter,
              arguments: TradingArguments(
                instrumentInfo: Instrument(
                  instrument: item.instrument,
                ),
                selectedIndex: TradeTabType.Quotes.index,
                contract: null,
                isIndexTrading: item.isIndex,
              ),
            ).then((value) {
              if (!context.mounted) return;
              cubit.fetchSpotScreenCurrentData();
            });
          },
          onTapTpSL: () {
            TradeBottomSheetFutureSlTp(
                context: context,
                contractName: item.symbolName,
                directionText: '多',
                directionColor: const Color(0xffF5222D),
                openPrice: item.buyAvgPrice,
                currentPrice: item.stockPrice,
                currency: item.currency,
                restNum: item.restNum,
                onPressedOk: (takeProfitValue, stopLossValue) async {
                  if (takeProfitValue != 0 && takeProfitValue <= item.buyAvgPrice) {
                    GPEasyLoading.showToast("take_profit_above_entry".tr()); // 止盈价格必须高于买入价格
                  } else if (stopLossValue != 0 && stopLossValue >= item.buyAvgPrice) {
                    GPEasyLoading.showToast("stop_loss_below_entry".tr()); // 止损价格必须低于买入价格
                  }
                  final flag = await cubit.setFuturesStopLine(
                      positionId: item.id, takeProfitValue: takeProfitValue, stopLossValue: stopLossValue);
                  if (flag) {
                    GPEasyLoading.showToast("success".tr());
                    await Future.delayed(const Duration(milliseconds: 500));
                    if (context.mounted) Navigator.of(context).pop();
                  }
                }).show();
          },
          onTapAdd: () async {
            await TradeAddMarginDialog(context, data: item, onPressedSubmit: (amount) async {
              final flag = await cubit.addMargin(positionId: item.id, amount: amount);
              if (flag) {
                GPEasyLoading.showToast("success".tr());
                await Future.delayed(const Duration(milliseconds: 500));
                if (context.mounted) Navigator.of(context).pop();
              }
            }).show();
          },
          onTapDetail: () {
            // AccountContractDetailCubit
            Navigator.pushNamed(context, routeSpotPositionDetail, arguments: {
              'id': item.id,
            });
          },
        ),
      OrderType.trades => AccountTradeCell(
          data: item,
          isLast: index == model.records.length - 1,
          onTap: () {
            context.read<TradingCubit>().getKlineDetailList(item.instrument, KlineConstants.options[0]);
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              builder: (_) {
                return MultiBlocProvider(
                  providers: [
                    BlocProvider.value(
                      value: context.read<TradingCubit>(),
                    ),
                    BlocProvider(
                      create: (_) => FTradeKLineCubit(FTradeKLineScrollRepository()),
                    ),
                  ],
                  child: Builder(builder: (innerContext) {
                    if (item.isCnFTrade) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        innerContext
                            .read<FTradeKLineCubit>()
                            .fetchTimeLineSubData(instrument: item.instrument, period: 'day');
                      });
                    }
                    return TradeBottomsheetOrders(data: item.toOrderRecord(), isTradeDetails: true, isTrading: false);
                  }),
                );
              },
            );
          },
        ),
      OrderType.order => AccountEntrustCell(
          data: item,
          isLast: index == model.records.length - 1,
          onTap: () {
            context.read<TradingCubit>().getKlineDetailList(item.instrument, KlineConstants.options[0]);
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              builder: (_) {
                return MultiBlocProvider(
                  providers: [
                    BlocProvider.value(
                      value: context.read<TradingCubit>(),
                    ),
                    BlocProvider(
                      create: (_) => FTradeKLineCubit(FTradeKLineScrollRepository()),
                    ),
                  ],
                  child: Builder(builder: (innerContext) {
                    if (item.isCnFTrade) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        innerContext
                            .read<FTradeKLineCubit>()
                            .fetchTimeLineSubData(instrument: item.instrument, period: 'day');
                      });
                    }
                    return TradeBottomsheetOrders(data: item.toOrderRecord());
                  }),
                );
              },
            );
          },
          onTapCancelBtn: () {
            showDialog(
              context: context,
              builder: (_) => BlocProvider.value(
                value: context.read<TradingCubit>(),
                child: CancelOrderDialog(orderId: item.id),
              ),
            );
          },
        ),
    };
  }

  Widget _buildShimmerWidget(OrderType type) {
    // 生成 3 个条目索引
    return Column(
      children: List.generate(3, (_) {
        return switch (type) {
          OrderType.positions => AccountPositionShimmerCell(marketCategory: widget.marketCategory),
          OrderType.trades => AccountTradeShimmerCell(),
          OrderType.order => AccountEntrustShimmerCell(),
        };
      }),
    );
  }
}
