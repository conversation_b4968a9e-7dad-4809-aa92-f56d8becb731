import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/apis/contract.dart';
import 'package:gp_stock_app/core/models/entities/contract/apply_trial_contract_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';

part 'apply_trial_contract_state.dart';


class ApplyTrialContractCubit extends Cubit<ApplyTrialContractState> {

  ApplyTrialContractCubit() : super(ApplyTrialContractState());

  /// Fetches contract activity data based on the specified type
  Future<void> getContractActivity({required int type, required double exchangeRate, required int parentType}) async {
    emit(state.copyWith(contractActivityFetchStatus: DataStatus.loading));

    final res = await ContractApi.fetchTrialContractConfig(type: type, parentType: parentType);
    if (res != null) {
      emit(state.copyWith(
        contractConfig: res,
        contractActivityFetchStatus: DataStatus.success,
      ));
      // Select the first market from the activity risk map if available
      final firstMarket = res.activityRiskMap.firstOrNull;
      if (firstMarket != null) {
        selectMarket(firstMarket, exchangeRate);
      }

      // Update amount list from the selected market
      // _updateAmountList(firstMarket);
      manageAmountList(res, exchangeRate);
    } else {
      emit(state.copyWith(
        contractActivityFetchStatus: DataStatus.failed,
      ));
    }
  }

  /// Updates the amount list dropdown values based on contract activity data
  void manageAmountList(ApplyTrialContractConfigEntity response, double exchangeRate) {
    final amount = response.activityRiskMap.first.giveAmount;
    final amountList = response.activityRiskMap.first.applyAmountList;

    if (amountList.isNotEmpty || amountList.isEmpty) {
        selectAmount(amount.toInt(), exchangeRate);
    } else {
      selectAmount(amountList.firstOrNull?.toInt(), exchangeRate);
    }
  }

  /// Selects a market and updates related state
  void selectMarket(ApplyTrialContractConfigActivityRiskMap market, double exchangeRate) {
    emit(state.copyWith(selectedMarket: market));
    _updateCurrency(market.marketType);
    calculateContractAmount(exchangeRate);
  }

  /// Updates the agreement status
  void updateIsAgree(bool value) => emit(state.copyWith(isAgree: value));

  /// Selects an amount and recalculates contract amount
  void selectAmount(int? amount, double exchangeRate) {
    emit(state.copyWith(selectedAmount: amount));
    calculateContractAmount(exchangeRate);
  }

  double calculateGiftAmount(double currentAmount, ApplyTrialContractConfigActivityRiskMap currentMarket) {
    final giveRatio = currentMarket.giveRatio;
    final giveAmount = currentMarket.giveAmount;
    if (giveAmount > 0) return giveAmount;
    return ((giveRatio) * (currentAmount / 100));
  }

  /// Calculates contract amounts based on selected values
  void calculateContractAmount(double exchangeRate) {
    getBonusContractAmount();
    try {
      final amount = state.selectedAmount;
      double principal = amount?.toDouble() ?? 0.0;
      final market = state.selectedMarket;
      final contractActivity = state.contractConfig;

      if (market == null || contractActivity == null) return;
      final bonusRatio = market.giveRatio;
      principal = principal + ((principal * bonusRatio) / 100);
      // Determine values based on contract type
      int multiple;
      double warnLossRadio;
      double closeLossRadio;
      double interestRate;

      // Experience contract type
      warnLossRadio = market.warnLossRadio;
      closeLossRadio = market.closeLossRadio;
      multiple = market.multiple;
      interestRate = market.interestRate;
      // Calculate base values
      final multipleAmount = principal * multiple;
      final total = multipleAmount + principal;
      final warnLine = multipleAmount + (principal * warnLossRadio / 100);
      final closeLine = multipleAmount + (principal * (100 - closeLossRadio) / 100);

      // Calculate interest
      final percent = multipleAmount * interestRate / 100;
      final interestCash = contractActivity.interestCash;
      final interest = interestCash - percent;
      // Calculate payment amount (converted to base currency)

      final payAmount = ((amount ?? 0) + (interest > 0 ? 0 : percent)) / exchangeRate;

      // Create contract model amount
      final contractModelAmount = ContractModelAmount(
        totalTadingFunds: total,
        lossWarningLine: warnLine,
        lossFlatLine: closeLine,
        interestRate: percent,
        deductionAmount: payAmount,
        actualAmount: principal,
        intrestDeductionAmount: interest > 0 ? percent : 0,
        bonus: calculateGiftAmount(principal, market),
      );

      emit(state.copyWith(contractModelAmount: contractModelAmount));
    } catch (e) {
      emit(state.copyWith(
        error: 'Error calculating contract amount: ${e.toString()}',
      ));
    }
  }

  /// Updates currency based on the market type
  void _updateCurrency(String market) => emit(state.copyWith(
          currency: switch (market) {
        'CN' => 'CNY',
        'HK' => 'HKD',
        'US' => 'USD',
        _ => state.currency,
      }));

  /// Applies for an experience contract
  Future<void> applyExperienceContract() async {
    emit(state.copyWith(applyExperienceContractStatus: DataStatus.loading));

    final market = state.selectedMarket;

    if (market == null) {
      emit(state.copyWith(
        applyExperienceContractStatus: DataStatus.failed,
        error: 'No market selected',
      ));
      return;
    }

    final flag = await ContractApi.applyTrialContract(
      activityId: market.activityId,
      activityRiskId: market.riskId,
      applyAmount: market.giveAmount,
      type: state.contractType?.value ?? 0,
    );

    if (flag) {
      emit(state.copyWith(
        applyExperienceContractStatus: DataStatus.success,
      ));
    } else {
      emit(state.copyWith(
        applyExperienceContractStatus: DataStatus.failed,
      ));
    }
  }

  /// Applies for an bonus contract
  Future<void> applyBonusContract() async {
    emit(state.copyWith(applyBonusContractStatus: DataStatus.loading));

    final market = state.selectedMarket;

    if (market == null) {
      emit(state.copyWith(
        applyBonusContractStatus: DataStatus.failed,
        error: 'No market selected',
      ));
      return;
    }

    final flag = await ContractApi.applyTrialContract(
      activityId: market.activityId,
      activityRiskId: market.riskId,
      applyAmount: (state.selectedAmount ?? 0).toDouble(),
      type: state.contractType?.value ?? 0,
      isBonus: true,
    );

    if (flag) {
      emit(state.copyWith(
        applyBonusContractStatus: DataStatus.success,
      ));
    } else {
      emit(state.copyWith(
        applyBonusContractStatus: DataStatus.failed,
      ));
    }
  }

  void setContractType(MainContractType contractType, ContractType contractSubType) =>
      emit(state.copyWith(contractType: contractType, contractSubType: contractSubType));

  Future<void> getBonusContractAmount() async {
    if (state.contractSubType != ContractType.bonus) return;
      final market = state.selectedMarket;
      if (market == null) return;

      final res = await ContractApi.getBonusContractAmount(
        activityId: market.activityId,
        activityRiskId: market.riskId,
        type: state.contractType?.value ?? 0,
        applyAmount: (state.selectedAmount ?? 0).toInt(),
      );

      if (res != null) {
        emit(state.copyWith(bonusContractCalculation: res));
      }
  }
}
