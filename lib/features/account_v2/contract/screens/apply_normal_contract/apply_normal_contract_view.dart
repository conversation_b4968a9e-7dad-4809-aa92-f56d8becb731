import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'apply_normal_contract_cubit.dart';
import 'apply_normal_contract_state.dart';

class ApplyNormalContractPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => ApplyNormalContractCubit(),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<ApplyNormalContractCubit>(context);

    return Container();
  }
}


