import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/apis/contract.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/features/contract/domain/repository/contract_repository.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';

part 'contract_withdraw_state.dart';

class ContractWithdrawCubit extends Cubit<ContractWithdrawState> {
  ContractWithdrawCubit(int contractId) : super(ContractWithdrawState(contractId: contractId)) {
    fetchWithdrawConfig();
  }

  Future<void> submitWithdraw({ required String applyAmount}) async {
    emit(state.copyWith(withdrawStatus: DataStatus.loading));
    final flag = await ContractApi.submitWithdraw(id: state.contractId, applyAmount: applyAmount);
    emit(state.copyWith(withdrawStatus: flag ? DataStatus.success : DataStatus.failed));
  }

  Future<void> fetchWithdrawConfig() async {
    emit(state.copyWith(withdrawAmountStatus: DataStatus.loading));
    final res = await ContractApi.fetchWithdrawalConfig(contractId: state.contractId);
    emit(state.copyWith(
      withdrawAmountStatus: res != null ? DataStatus.success : DataStatus.failed,
      withdrawConfig: res,
    ));
  }
}
