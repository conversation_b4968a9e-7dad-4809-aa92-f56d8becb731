part of 'contract_terminate_cubit.dart';

class ContractTerminateState extends Equatable {
  final DataStatus terminateStatus;
  const ContractTerminateState({this.terminateStatus = DataStatus.idle});

  @override
  List<Object?> get props => [terminateStatus];

  ContractTerminateState copyWith({DataStatus? terminateStatus, String? errorMessage}) {
    return ContractTerminateState(
      terminateStatus: terminateStatus ?? this.terminateStatus,
    );
  }
}
