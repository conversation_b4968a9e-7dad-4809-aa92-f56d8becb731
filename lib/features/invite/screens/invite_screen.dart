import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/invite/logic/invite/invite_cubit.dart';
import 'package:gp_stock_app/features/invite/logic/invite/invite_state.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/api/network/endpoint/urls.dart';
import '../../../shared/widgets/error/error_retry_widget.dart';
import '../../../shared/widgets/flip_text.dart';
import '../widgets/invite_shimmer_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import '../../../shared/widgets/buttons/common_button.dart';
import '../../../shared/widgets/shadow_box.dart';

class InviteScreen extends StatefulWidget {
  final AppSkinStyle? skinStyle;

  const InviteScreen({super.key, this.skinStyle});

  @override
  State<InviteScreen> createState() => _InviteScreenState();
}

class _InviteScreenState extends State<InviteScreen> {
  @override
  void initState() {
    super.initState();
    context.read<InviteCubit>().getInviteDetails();
  }

  @override
  Widget build(BuildContext context) {
    // Determine which skin style to use
    final currentSkinStyle = widget.skinStyle ?? AppConfig.instance.skinStyle;

    return Scaffold(
      appBar: _buildAppBar(context, currentSkinStyle),
      body: BlocBuilder<InviteCubit, InviteState>(
        builder: (context, state) {
          if (state.status.isLoading) {
            return const InviteScreenShimmer();
          }

          if (state.status.isFailed) {
            return ErrorRetryWidget(
              errorMessage: state.error,
              onRetry: () => context.read<InviteCubit>().getInviteDetails(),
            );
          }

          return _buildBody(context, state, currentSkinStyle);
        },
      ),
    );
  }

  /// Build app bar based on skin style
  PreferredSizeWidget _buildAppBar(BuildContext context, AppSkinStyle skinStyle) {
    switch (skinStyle) {
      case AppSkinStyle.kGP:
        // Use original GP style app bar
        return AppBar(
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: context.colorTheme.textSecondary,
              size: 20.gw,
            ),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text('inviteAndEarn'.tr(), style: context.textTheme.secondary.fs18.w600),
          backgroundColor: context.colorTheme.inviteBackgroundStart,
          elevation: 0,
          foregroundColor: Colors.white,
        );
      case AppSkinStyle.kTemplateA:
      case AppSkinStyle.kTemplateB:
      case AppSkinStyle.kTemplateC:
      case AppSkinStyle.kTemplateD:
        // Use new template style app bar
        return AppBar(
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: Colors.white,
              size: 20.gw,
            ),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text('inviteAndEarn'.tr(), style: context.textTheme.primary.fs18.w600.copyWith(color: Colors.white)),
          backgroundColor: context.colorTheme.inviteBackgroundStart,
          elevation: 0,
          foregroundColor: Colors.white,
        );
    }
  }

  /// Build body based on skin style
  Widget _buildBody(BuildContext context, InviteState state, AppSkinStyle skinStyle) {
    switch (skinStyle) {
      case AppSkinStyle.kGP:
        // Use original GP style body
        return _buildGPBody(context, state);
      case AppSkinStyle.kTemplateA:
      case AppSkinStyle.kTemplateB:
      case AppSkinStyle.kTemplateC:
      case AppSkinStyle.kTemplateD:
        // Use new template style body
        return _buildTemplateBody(context, state);
    }
  }

  /// Build GP style body (original design)
  Widget _buildGPBody(BuildContext context, InviteState state) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            context.colorTheme.inviteBackgroundStart,
            context.colorTheme.inviteBackgroundEnd,
          ],
        ),
      ),
      child: Column(
        children: [
          _buildGPHeader(context),
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.gw),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: context.theme.dividerColor.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.all(
                          Radius.circular(12.gr),
                        ),
                      ),
                      margin: EdgeInsets.only(top: 10.gh),
                      padding: EdgeInsets.all(16.gr),
                      child: Column(
                        children: [
                          _buildGPReferralOptions(state),
                          SizedBox(height: 20.gh),
                          _buildGPGenerateButton(),
                        ],
                      ),
                    ),
                    SizedBox(height: 20.gh),
                    _buildGPCommissionInfo(state),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build template style body (new design)
  Widget _buildTemplateBody(BuildContext context, InviteState state) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            context.colorTheme.inviteBackgroundStart,
            context.colorTheme.inviteBackgroundEnd,
          ],
        ),
      ),
      child: Column(
        children: [
          _buildTemplateHeader(context),
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.gw),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    ShadowBox(
                      padding: EdgeInsets.all(16.gr),
                      borderRadius: BorderRadius.circular(12.gr),
                      child: Column(
                        children: [
                          _buildTemplateReferralOptions(state),
                          SizedBox(height: 20.gh),
                          _buildTemplateGenerateButton(),
                        ],
                      ),
                    ),
                    SizedBox(height: 20.gh),
                    _buildTemplateCommissionInfo(state),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGPHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'inviteAndEarnDescription'.tr(),
          style: context.textTheme.primary.fs24.w700.copyWith(
            color: Colors.amber[50]!,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8.gh),
        Text(
          'performCashTradeToEarn'.tr(),
          style: context.textTheme.regular.fs16.copyWith(
            color: Colors.amber[50]!,
          ),
          textAlign: TextAlign.center,
        ),
        16.verticalSpace,
        Stack(
          alignment: Alignment.center,
          children: [
            SizedBox(
              height: 150.gh,
              width: 1.gsw,
              child: Image.asset(
                Assets.grabCoinsIcon,
                fit: BoxFit.cover,
              ),
            ),
            Positioned(
              bottom: 0,
              child: Container(
                width: 330.gw,
                padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 6.gh),
                decoration: BoxDecoration(
                  color: context.theme.dividerColor.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(20.gr),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'levelingUpToEarnMore'.tr(),
                      style: context.textTheme.secondary.fs12,
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 6.gh),
                      decoration: BoxDecoration(
                        color: const Color(0xFFE06821),
                        borderRadius: BorderRadius.circular(20.gr),
                      ),
                      child: Text('GO', style: context.textTheme.secondary.fs12.w700),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGPReferralOptions(InviteState state) {
    final inviteCode = state.inviteDetail?.inviteCode ?? '';
    final inviteLink = '${Urls.inviteLink}$inviteCode';

    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () {
              _showQrCodeDialog(context, inviteLink);
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 16.gh, horizontal: 16.gw),
              decoration: BoxDecoration(
                color: Colors.pink[50]!,
                borderRadius: BorderRadius.circular(10.gr),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.qr_code_2,
                    size: 22.gr,
                    color: context.colorTheme.stockRed,
                  ),
                  SizedBox(height: 10.gh),
                  Text(
                    'qrCode'.tr(),
                    style: context.textTheme.stockRed,
                  ),
                ],
              ),
            ),
          ),
        ),
        SizedBox(width: 16.gw),
        Expanded(
          child: GestureDetector(
            onTap: () {
              Clipboard.setData(ClipboardData(text: inviteCode));
              Helper.showFlutterToast('inviteCodeCopiedToClipboard'.tr());
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 16.gh, horizontal: 16.gw),
              decoration: BoxDecoration(
                color: Colors.pink[50]!,
                borderRadius: BorderRadius.circular(10.gr),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Flexible(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              inviteCode,
                              style: context.textTheme.stockRed,
                              overflow: TextOverflow.ellipsis,
                            ),
                            8.horizontalSpace,
                            Icon(
                              Icons.copy,
                              size: 20.gr,
                              color: context.colorTheme.stockRed,
                            ),
                          ],
                        ),
                        10.verticalSpace,
                        Text(
                          'inviteCode'.tr(),
                          style: context.textTheme.stockRed,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 8.gw),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGPGenerateButton() {
    return BlocBuilder<InviteCubit, InviteState>(
      builder: (context, state) {
        final inviteCode = state.inviteDetail?.inviteCode ?? '';
        final inviteLink = '${Urls.inviteLink}$inviteCode';

        return ElevatedButton.icon(
          onPressed: () {
            Clipboard.setData(ClipboardData(text: inviteLink));
            Helper.showFlutterToast('inviteLinkCopiedToClipboard'.tr());
          },
          style: ElevatedButton.styleFrom(
            minimumSize: Size(double.infinity, 38.gh),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.gr),
            ),
          ),
          icon: Icon(Icons.link, color: context.theme.hintColor),
          label: Text(
            'generateInviteLink'.tr(),
            style: context.textTheme.title.fs16.w600,
          ),
        );
      },
    );
  }

  Widget _buildGPCommissionInfo(InviteState state) {
    return Container(
      padding: EdgeInsets.all(16.gr),
      decoration: BoxDecoration(
        color: context.theme.dividerColor.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(10.gr),
        boxShadow: [
          BoxShadow(
            color: context.theme.shadowColor,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'commissionRate'.tr(),
                      style: context.textTheme.regular,
                    ),
                    SizedBox(height: 10.gh),
                    Text(
                      '${state.inviteDetail?.commissionRate?.toStringAsFixed(2) ?? 0.00}%',
                      style: context.textTheme.stockRed.fs24.w700,
                    ),
                  ],
                ),
              ),
              Container(
                height: 50.gh,
                width: 1,
                color: context.theme.dividerColor,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'interestRateRatio'.tr(),
                      style: context.textTheme.regular,
                    ),
                    SizedBox(height: 10.gh),
                    Text(
                      '${state.inviteDetail?.interestRate?.toStringAsFixed(2) ?? 0.00}%',
                      style: context.textTheme.stockRed.fs24.w700,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 20.gh),
          Divider(
            height: 1,
            color: context.theme.dividerColor,
          ),
          SizedBox(height: 20.gh),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'myCommission'.tr(),
                style: context.textTheme.regular,
              ),
              FlipText(
                state.inviteDetail?.totalCommission ?? 0.00,
                style: context.textTheme.regular.w700,
                isCurrency: true,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Template style header (new design)
  Widget _buildTemplateHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 12.gh),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            'inviteAndEarnDescription'.tr(),
            style: context.textTheme.primary.w700.copyWith(
              fontSize: 36.gsp,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            'performCashTradeToEarn'.tr(),
            style: context.textTheme.secondary.fs16.w700.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
            textAlign: TextAlign.center,
          ),
          16.verticalSpace,
          Container(
            height: 120.gh,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.gr),
            ),
            child: Center(
              child: Icon(
                Icons.card_giftcard,
                size: 48.gr,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
          ),
          16.verticalSpace,
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 8.gh),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.6),
              borderRadius: BorderRadius.circular(20.gr),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'levelingUpToEarnMore'.tr(),
                  style: context.textTheme.primary.fs12,
                ),
                6.horizontalSpace,
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 6.gh),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30.gr),
                    color: Colors.white,
                  ),
                  child: Text(
                    'GO',
                    style: context.textTheme.secondary.fs12.w700.copyWith(
                      color: context.colorTheme.stockRed,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Template style referral options (new design)
  Widget _buildTemplateReferralOptions(InviteState state) {
    final inviteCode = state.inviteDetail?.inviteCode ?? '';
    final inviteLink = '${Urls.inviteLink}$inviteCode';

    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () {
              _showQrCodeDialog(context, inviteLink);
            },
            child: Container(
              height: 80.gh,
              padding: EdgeInsets.symmetric(vertical: 10.gh, horizontal: 16.gw),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    context.colorTheme.textPrimary.withValues(alpha: 0.1),
                    context.colorTheme.textPrimary.withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(12.gr),
                border: Border.all(
                  color: context.colorTheme.textPrimary.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.qr_code_2,
                    size: 28.gr,
                    color: context.colorTheme.textPrimary,
                  ),
                  SizedBox(height: 8.gh),
                  Text(
                    'qrCode'.tr(),
                    style: context.textTheme.primary.fs12.w500,
                  ),
                ],
              ),
            ),
          ),
        ),
        SizedBox(width: 12.gw),
        Expanded(
          child: GestureDetector(
            onTap: () {
              Clipboard.setData(ClipboardData(text: inviteCode));
              Helper.showFlutterToast('inviteCodeCopiedToClipboard'.tr());
            },
            child: Container(
              height: 70.gh,
              padding: EdgeInsets.symmetric(vertical: 10.gh, horizontal: 16.gw),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    context.colorTheme.textPrimary.withValues(alpha: 0.1),
                    context.colorTheme.textPrimary.withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(12.gr),
                border: Border.all(
                  color: context.colorTheme.textPrimary.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Flexible(
                        child: Text(
                          inviteCode,
                          style: context.textTheme.primary.fs12.w600,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      4.horizontalSpace,
                      Icon(
                        Icons.copy,
                        size: 16.gr,
                        color: context.colorTheme.textPrimary,
                      ),
                    ],
                  ),
                  8.verticalSpace,
                  Text(
                    'inviteCode'.tr(),
                    style: context.textTheme.primary.fs12.w500,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Template style generate button (new design)
  Widget _buildTemplateGenerateButton() {
    return BlocBuilder<InviteCubit, InviteState>(
      builder: (context, state) {
        final inviteCode = state.inviteDetail?.inviteCode ?? '';
        final inviteLink = '${Urls.inviteLink}$inviteCode';

        return CommonButton(
          title: 'generateInviteLink'.tr(),
          style: CommonButtonStyle.primary,
          height: 44.gh,
          onPressed: () {
            Clipboard.setData(ClipboardData(text: inviteLink));
            Helper.showFlutterToast('inviteLinkCopiedToClipboard'.tr());
          },
          prefix: Icon(
            Icons.link,
            color: Colors.white,
            size: 18.gr,
          ),
        );
      },
    );
  }

  /// Template style commission info (new design)
  Widget _buildTemplateCommissionInfo(InviteState state) {
    return ShadowBox(
      padding: EdgeInsets.all(20.gr),
      borderRadius: BorderRadius.circular(12.gr),
      child: Column(
        children: [
          // Only wrap commissionDetails in a Stack with SVG background
          Container(
            padding: EdgeInsets.symmetric(vertical: 8.gh, horizontal: 12.gw),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.gr),
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Positioned.fill(
                  child: SvgPicture.asset(
                    Assets.inviteBtnBg,
                    fit: BoxFit.cover,
                  ),
                ),
                Text(
                  'commissionDetails'.tr(),
                  style: context.textTheme.primary.w600,
                ),
              ],
            ),
          ),
          8.verticalSpace,
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'commissionRate'.tr(),
                      style: context.textTheme.highlight,
                    ),
                    SizedBox(height: 8.gh),
                    Text(
                      '${state.inviteDetail?.commissionRate?.toStringAsFixed(2) ?? 0.00}%',
                      style: context.textTheme.primary.w700.ffAkz.copyWith(fontSize: 30.gsp),
                    ),
                  ],
                ),
              ),
              Container(
                height: 40.gh,
                width: 1,
                color: context.colorTheme.border,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'interestRateRatio'.tr(),
                      style: context.textTheme.highlight,
                    ),
                    SizedBox(height: 8.gh),
                    Text(
                      '${state.inviteDetail?.interestRate?.toStringAsFixed(2) ?? 0.00}%',
                      style: context.textTheme.primary.w700.ffAkz.copyWith(fontSize: 30.gsp),
                    ),
                  ],
                ),
              ),
            ],
          ),
          8.verticalSpace,
          Divider(
            height: 1,
            color: context.theme.dividerColor,
          ),
          12.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'myCommission'.tr(),
                style: context.textTheme.regular,
              ),
              FlipText(
                state.inviteDetail?.totalCommission ?? 0.00,
                style: context.textTheme.primary.fs16.w700,
                isCurrency: true,
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showQrCodeDialog(BuildContext context, String inviteLink) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: context.theme.cardColor,
        child: Container(
          padding: EdgeInsets.all(24.gr),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              QrImageView(
                data: inviteLink,
                version: QrVersions.auto,
                size: 200.0,
                backgroundColor: context.theme.cardColor,
                padding: EdgeInsets.all(16.gr),
              ),
              SizedBox(height: 16.gh),
              Text(
                'scanQrCodeToAdd'.tr(),
                style: context.textTheme.regular.fs16.w600,
              ),
              SizedBox(height: 8.gh),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'cancel'.tr(),
                      style: TextStyle(
                        color: context.colorTheme.stockRed,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
